package com.xfposthouse.module.vip.entity

import java.math.BigDecimal

enum class BenefitType(val code: Int) {
    SPACE(1), // 存储空间
    VOICE_COUNT(2), // 语音记账次数
}

data class MemberShipEntity(
    var membershipId: Int? = null, // 主键，可选
    var membershipType: Int = 0, //  -- 会员类型 (1: 普通会员, 2: 黄金会员)
    var membershipPlan: Int = 0, // 会员计划 (1, 月会员 2，季度会员 3，年会员)
    var price: BigDecimal = BigDecimal(0), // 当前会员价格
    var originalPrice: BigDecimal? = null, // 划线价（原价），可选
    var durationDays: Int = 0, // 有效期天数
    var description: String? = null, // 描述
    var membershipLevel: Int? = null,
    var title: String? = null,
    var purchaseId: String? = null,
    var benefits: List<MembershipBenefitEntity> = emptyList() // 会员对应的权益列表
) {
    companion object {
        fun fromJson(map: Map<String, Any>): MemberShipEntity {
            return MemberShipEntity(
                membershipType = map["membershipType"] as Int,
                membershipPlan = map["membershipPlan"] as Int,
                price = BigDecimal(map["price"].toString().toDouble()),
                originalPrice = BigDecimal(map["originalPrice"].toString().toDouble()),
                durationDays = map["durationDays"] as Int,
                membershipLevel = map["membershipLevel"] as? Int,
                description = map["description"] as String,
                title = map["title"] as String,
                    purchaseId = map["purchaseId"] as String,
            )
        }
    }
}

data class MembershipBenefitEntity(
    var id: Int? = null,                 // 权益ID
    var benefitType: Int = 0,               // 权益类型（1：存储空间，2：语音记账次数）
    var benefitName: String? = null,            // 权益名称（例如：存储空间、上传限制等）
    var benefitValue: String? = null,           // 权益值（例如：10GB存储空间，100MB上传限制等）
    var description: String? = null,    // 权益描述（可选）
    var createdAt: Long? = null, // 记录创建时间
    var updatedAt: Long? = null // 记录更新时间
) {
    companion object {
        fun fromJson(map: Map<String, Any>): MembershipBenefitEntity {
            return MembershipBenefitEntity(
                id = map["id"] as? Int,
                benefitType = map["benefitType"] as Int,
                benefitName = map["benefitName"] as String,
                benefitValue = map["benefitValue"] as String,
                description = map["description"] as? String,
                createdAt = map["createdAt"] as? Long,
                updatedAt = map["updatedAt"] as? Long,
            )
        }
    }
}


data class MembershipBenefitRelationEntity(
    var membershipId: Int? = null,              // 会员类型ID
    var benefitId: Int? = null,                 // 权益ID
) {
    companion object {
        fun fromJson(map: Map<String, Any>): MembershipBenefitRelationEntity {
            return MembershipBenefitRelationEntity(
                membershipId = map["membershipId"] as Int,
                benefitId = map["benefitId"] as Int,
            )
        }
    }
}