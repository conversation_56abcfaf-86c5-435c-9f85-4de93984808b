package com.xfposthouse.config

import com.xfposthouse.logger.XFLogInterceptor
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Configuration
import org.springframework.web.servlet.config.annotation.CorsRegistry
import org.springframework.web.servlet.config.annotation.InterceptorRegistry
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer

@Configuration
class AllowsConfig : WebMvcConfigurer {

    @Autowired
    private lateinit var logInterceptor: XFLogInterceptor

    override fun addCorsMappings(registry: CorsRegistry) {
        registry.addMapping("/**")
            .allowedOrigins("http://adult-admin.xftiger.com", "http://localhost:8086")
            .allowedMethods("GET", "POST", "DELETE", "PUT", "OPTIONS")
            .allowedHeaders("*")
            .allowCredentials(true)
            .maxAge(31536000)
    }

    override fun addInterceptors(registry: InterceptorRegistry) {
        registry.addInterceptor(logInterceptor)
            .addPathPatterns("/**")
            .excludePathPatterns("/static/**")
//        super.addInterceptors(registry)
    }
}