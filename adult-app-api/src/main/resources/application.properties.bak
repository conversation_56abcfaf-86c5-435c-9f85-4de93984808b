

server.port=8086
spring.datasource.url=****************************************************************************************************************************
spring.datasource.password=Dbzq930801

#server.port=8086
#spring.datasource.url=***************************************************************************************************************************************************************************
#spring.datasource.password=zqx19930304
##spring.datasource.password=Zqx19930304

spring.datasource.username=root
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
logging.config=classpath:logback-spring.xml
#logging.file.path=./logs
#logging.logback.rollingpolicy.max-file-size=10MB
#logging.logback.rollingpolicy.max-history=30
#logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n