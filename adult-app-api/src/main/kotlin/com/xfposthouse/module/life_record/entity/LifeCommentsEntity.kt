package com.xfposthouse.module.life_record.entity

open class LifeCommentsEntity {

     var id: Long = 0L

     var postId: Long? = null

     var userId: Long? = null

     var content: String? = null

     var parentId: Long? = null

     var status: Int = 1

     var createdAt: Long? = null

    var updatedAt: Long? = null

    var toUserId: Long? = null

    var userName: String? = null

    var userAvatar: String? = null

    var toUserName: String? = null

    var replyCount: Int? = null

    companion object {
        fun fromJson(json: Map<String, Any>): LifeCommentsEntity {
            val lifeCommentEntity = LifeCommentsEntity()
            lifeCommentEntity.postId = json.parseNullableLong("postId")
            lifeCommentEntity.userId = json.parseLong("userId", required = true)
            lifeCommentEntity.content = json.parseString("content")
            lifeCommentEntity.parentId = json.parseNullableLong("parentId")
            lifeCommentEntity.toUserId = json.parseNullableLong("toUserId")
            lifeCommentEntity.status = json.parseInt("status") ?: 1
            lifeCommentEntity.updatedAt = json.parseNullableLong("updatedAt")
            lifeCommentEntity.createdAt = json.parseNullableLong("createdAt")
            return lifeCommentEntity
        }

        // 扩展函数：解析Long类型（强制要求字段存在）
        private fun Map<String, Any>.parseLong(key: String, required: Boolean): Long {
            return this[key]?.toString()?.toLongOrNull()
                ?: if (required) throw IllegalArgumentException("Missing required field: $key") else 0L
        }

        // 扩展函数：解析可空Long类型
        private fun Map<String, Any>.parseNullableLong(key: String): Long? {
            return this[key]?.toString()?.toLongOrNull()
        }

        // 扩展函数：解析String类型（强制要求字段存在）
        private fun Map<String, Any>.parseString(key: String): String {
            return this[key] as? String
                ?: throw IllegalArgumentException("Field $key is not a String or is missing")
        }

        // 扩展函数：解析Int类型（允许空值，返回null）
        private fun Map<String, Any>.parseInt(key: String): Int? {
            return when (val value = this[key]) {
                is Number -> value.toInt()
                is String -> value.toIntOrNull()
                else -> null
            }
        }
    }
}