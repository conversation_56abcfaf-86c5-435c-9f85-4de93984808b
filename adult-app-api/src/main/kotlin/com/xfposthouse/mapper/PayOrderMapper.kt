package com.xfposthouse.mapper

import com.xfposthouse.pay.entity.PayOrderEntity
import org.apache.ibatis.annotations.*
import org.springframework.stereotype.Repository
import com.mybatisflex.core.BaseMapper

@Repository
interface PayOrderMapper: BaseMapper<PayOrderEntity> {

    @Select("SELECT * FROM pay_orders WHERE id = #{id}")
    @Results(id = "payOrderMap", value = [
        Result(property = "id", column = "id"),
        Result(property = "orderNo", column = "order_no"),
        Result(property = "userId", column = "user_id"),
        Result(property = "payMethod", column = "pay_method"),
        Result(property = "payState", column = "pay_state"),
        Result(property = "buyerPayNo", column = "buyer_pay_no"),
        Result(property = "buyerPayAmount", column = "buyer_pay_amount"),
        Result(property = "tradeNo", column = "trade_no"),
        Result(property = "orderType", column = "order_type"),
        Result(property = "productId", column = "product_id"),
        Result(property = "productPrice", column = "product_price"),
        Result(property = "productDesc", column = "product_desc"),
        Result(property = "productTitle", column = "product_title")
    ])
    fun getPayOrderById(id: Int): PayOrderEntity?

    @Select("SELECT * FROM pay_orders WHERE order_no = #{orderNo}")
    @ResultMap("payOrderMap")
    fun getPayOrderByOrderNo(orderNo: String): PayOrderEntity?

    @Insert("""
        INSERT INTO pay_orders 
        (order_no, user_id, pay_method, pay_state, buyer_pay_no, buyer_pay_amount, trade_no, 
         order_type, product_id, product_price, product_desc, product_title) 
        VALUES 
        (#{orderNo}, #{userId}, #{payMethod}, #{payState}, #{buyerPayNo}, #{buyerPayAmount}, #{tradeNo}, 
         #{orderType}, #{productId}, #{productPrice}, #{productDesc}, #{productTitle})
    """)
    fun insertPayOrder(order: PayOrderEntity): Int

    @Update("""
        UPDATE pay_orders SET 
            order_no = COALESCE(#{orderNo}, order_no),
            user_id = COALESCE(#{userId}, user_id),
            pay_method = COALESCE(#{payMethod}, pay_method),
            pay_state = COALESCE(#{payState}, pay_state),
            buyer_pay_no = COALESCE(#{buyerPayNo}, buyer_pay_no),
            buyer_pay_amount = COALESCE(#{buyerPayAmount}, buyer_pay_amount),
            trade_no = COALESCE(#{tradeNo}, trade_no),
            order_type = COALESCE(#{orderType}, order_type),
            product_id = COALESCE(#{productId}, product_id),
            product_price = COALESCE(#{productPrice}, product_price),
            product_desc = COALESCE(#{productDesc}, product_desc),
            product_title = COALESCE(#{productTitle}, product_title)
        WHERE id = #{id}
    """)
    fun updatePayOrder(order: PayOrderEntity): Int

    @Delete("DELETE FROM pay_orders WHERE id = #{id}")
    fun deletePayOrderById(id: Int): Int

    @Select("SELECT * FROM pay_orders")
    @ResultMap("payOrderMap")
    fun getPayOrderList(): List<PayOrderEntity>

    // 通过用户 ID 查询订单
    @Select("SELECT * FROM pay_orders WHERE user_id = #{userId}")
    @ResultMap("payOrderMap")
    fun getPayOrdersByUserId(userId: Int): List<PayOrderEntity>

    // 批量删除订单
    @Delete("<script> DELETE FROM pay_orders WHERE id IN <foreach collection='orderIds' item='id' open='(' separator=',' close=')' >#{id}</foreach> </script>")
    fun deletePayOrders(@Param("orderIds") orderIds: List<Int>): Int
}
