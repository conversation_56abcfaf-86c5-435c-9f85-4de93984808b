package com.xfposthouse.mapper

import com.xfposthouse.user.entity.XFAdminBlockEntity
import com.xfposthouse.user.entity.XFUserEntity
import org.apache.ibatis.annotations.*
import org.springframework.stereotype.Repository
import com.mybatisflex.core.BaseMapper

@Repository
interface UserBlackMapper : BaseMapper<XFUserEntity> {
    // 幂等插入（利用 ON DUPLICATE KEY 替代 INSERT IGNORE）
    @Insert("""
        INSERT INTO user_blacklist (user_id, blocked_user_id)
        VALUES (#{userId}, #{blockedUserId})
        ON DUPLICATE KEY UPDATE id = id
    """)
    fun insertSafely(userId: Int, blockedUserId: Int): Int

    // 删除拉黑关系
    @Delete("""
        DELETE FROM user_blacklist 
        WHERE user_id = #{userId} 
          AND blocked_user_id = #{blockedUserId}
    """)
    fun deleteByRelation(userId: Int, blockedUserId: Int): Int

    // 高性能存在性检查（直接使用覆盖索引）
    @Select("""
        SELECT COUNT(1)
        FROM user_blacklist 
        WHERE user_id = #{userId} 
          AND blocked_user_id = #{blockedUserId}
        LIMIT 1
    """)
    fun exists(@Param("userId") userId: Int,
               @Param("blockedUserId") blockedUserId: Int): Boolean

    // 分页查询（使用索引 idx_relation）
    @Select("""
        SELECT blocked_user_id 
        FROM user_blacklist 
        WHERE user_id = #{userId}
        ORDER BY created_at DESC
        LIMIT #{offset}, #{pageSize}
    """)
    fun listBlockedUsers(
        @Param("userId") userId: Int,
        @Param("offset") offset: Int,
        @Param("pageSize") pageSize: Int
    ): List<Int>

    /**
     * 记录屏蔽操作
     * @return 影响行数
     */
    @Insert("""
        INSERT INTO admin_block (admin_id, target_user_id, block_type, reason)
        VALUES (#{adminId}, #{targetUserId}, #{blockType}, #{reason})
    """)
    @Options(useGeneratedKeys = true, keyProperty = "actionId")
    fun insertBlockRecord(block: XFAdminBlockEntity): Int
}
