import org.gradle.internal.classpath.Instrumented.systemProperty
import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

plugins {
    kotlin("jvm") version "2.1.20"
    kotlin("plugin.spring") version "2.1.20"
}

group = "com.adult"
version = "1.0.0"
java.sourceCompatibility = JavaVersion.VERSION_21

// 为所有子项目配置 repositories
allprojects {
    repositories {
        mavenCentral()
    }
    
    // 全局排除commons-logging
    configurations.all {
        exclude(group = "commons-logging", module = "commons-logging")
    }
}

tasks.withType<KotlinCompile> {
    compilerOptions {
        freeCompilerArgs.set(listOf("-Xjsr305=strict"))
        jvmTarget.set(JvmTarget.JVM_17)
    }
}

tasks.withType<Test> {
    useJUnitPlatform()
}
