package discipline

import com.xfposthouse.entity.DisciplineNodeEntity
import com.xfposthouse.entity.DisciplinePlanEntity
import com.xfposthouse.mapper.DisciplineMapper
import com.xfposthouse.mapper.DisciplineNodeMapper
import org.springframework.stereotype.Service

@Service
class DisciplineServer(
    private val planMapper: DisciplineMapper,
    private val nodeMapper: DisciplineNodeMapper,
) {
    fun createPlan(plan: DisciplinePlanEntity): Boolean {
        return planMapper.insert(plan) > 0
    }

    fun createPlanNodes(plans: List<DisciplineNodeEntity>): Bo<PERSON>an {
        return nodeMapper.insertBatch(plans) > 0
    }
}