package com.xfposthouse.pay.entity


data class PayOrderEntity(
        var id: Int = 0,
        /// 订单号
        var orderNo: String = "",
        /// 用户 ID，关联用户表
        var userId: Int = 0,
        /// 支付方式 1, 支付宝 2，微信 0,其他, 3, 苹果内购
        var payMethod: Int = 0,
        /// 1，待支付 2，支付成功 3，支付失败 4，取消支付 5, 支付中 0，其他
        var payState: Int = 0,
        /// 买家支付宝账号
        var buyerPayNo: String? = null,
        /// 用户支付金额
        var buyerPayAmount: Float? = null,
        /// 交易流水号
        var tradeNo: String? = null,
        /// 订单类型 - 1, 购买会员
        var orderType: Int = 0,
        /// 商品id
        var productId: String? = null,
        /// 商品价格
        var productPrice: Double? = null,
        /// 商品描述
        var productDesc: String? = null,
        /// 商品标题
        var productTitle: String? = null
) {
    companion object {
        fun fromJson(map: Map<String, Any>): PayOrderEntity {
            return PayOrderEntity(
                    id = map["id"] as? Int ?: 0,
                    orderNo = map["orderNo"] as? String ?: "",
                    payMethod = map["payMethod"] as? Int ?: 0,
                    payState = map["payState"] as? Int ?: 0,
                    buyerPayNo = map["buyerPayNo"] as? String,
                    buyerPayAmount = map["buyerPayAmount"] as? Float,
                    tradeNo = map["tradeNo"] as? String,
                    orderType = map["orderType"] as? Int ?: 1,
                    productId = map["productId"] as? String,
                    productPrice = map["productPrice"] as? Double,
                    productDesc = map["productDesc"] as? String,
                    productTitle = map["productTitle"] as? String
            )
        }
    }
}
