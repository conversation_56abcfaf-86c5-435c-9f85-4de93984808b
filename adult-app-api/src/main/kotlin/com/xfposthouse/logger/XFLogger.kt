package com.xfposthouse.logger
import com.google.gson.Gson
import io.github.oshai.kotlinlogging.KotlinLogging
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.springframework.stereotype.Component
import org.springframework.web.servlet.HandlerInterceptor
import org.springframework.web.util.ContentCachingRequestWrapper
import org.springframework.web.util.ContentCachingResponseWrapper
import java.nio.charset.StandardCharsets

@Component
class XFLogInterceptor : HandlerInterceptor {

    // 使用kotlin-logging的伴生对象
    private val logger = KotlinLogging.logger {}

    override fun preHandle(
        request: HttpServletRequest,
        response: HttpServletResponse,
        handler: Any
    ): Boolean {
        request.setAttribute("startTime", System.currentTimeMillis())
        return true
    }

    override fun afterCompletion(
        request: HttpServletRequest,
        response: HttpServletResponse,
        handler: Any,
        ex: Exception?
    ) {
        val startTime = request.getAttribute("startTime") as? Long ?: return
        val duration = System.currentTimeMillis() - startTime

        val req = request as? ContentCachingRequestWrapper
        val res = response as? ContentCachingResponseWrapper
        if (request.requestURI == "/error") {
            return
        }
        val headers = mutableMapOf<String, String>()
        val headerNames = request.headerNames
        if (headerNames != null) {
            while (headerNames.hasMoreElements()) {
                val name = headerNames.nextElement()
                val value = request.getHeader(name)
                if (name == "token") {
                    continue
                }
                headers[name] = value
            }
        }
        val requestBody = req?.let { String(it.contentAsByteArray, StandardCharsets.UTF_8) }
        val formattedRequestBody = requestBody?.replace(Regex("\\s+"), " ")
        val responseBody = res?.let { String(it.contentAsByteArray, StandardCharsets.UTF_8) }

        val errStr = ex?.message
        val info = mapOf(
            "URI" to request.requestURI,
            "ip" to req?.remoteAddr,
            "Method" to request.method,
            "headers" to headers,
            "body" to formattedRequestBody,
            "response" to responseBody,
            "duration" to duration,
            "err" to errStr,)
        val gson = Gson()
        val jsonString = gson.toJson(info)
        logger.info {
            jsonString
        }
    }
}
