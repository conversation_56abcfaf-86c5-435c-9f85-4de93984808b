package com.xfposthouse.module.vip.mapper

import com.xfposthouse.module.vip.entity.MembershipBenefitEntity
import org.apache.ibatis.annotations.*
import org.springframework.stereotype.Repository
import com.mybatisflex.core.BaseMapper

@Repository
interface MembershipBenefitMapper: BaseMapper<MembershipBenefitEntity> {

    // 根据ID查询单个会员权益
    @Select("SELECT * FROM membership_benefits WHERE id = #{id}")
    fun getBenefitById(id: Int): MembershipBenefitEntity?

    // 根据权益类型查询所有权益
    @Select("SELECT * FROM membership_benefits WHERE benefit_type = #{benefitType}")
    fun getBenefitsByType(benefitType: Int): List<MembershipBenefitEntity>

    @Select("""
        SELECT 
            mb.id AS benefitId, 
            mb.benefit_type AS benefitType,
            mb.benefit_name AS benefitName,
            mb.benefit_value AS benefitValue,
            mb.description AS description
        FROM membership_benefits mb
        JOIN membership_benefit_relations mbr ON mb.id = mbr.benefit_id
        WHERE mbr.membership_id = #{membershipId}
    """)
    fun findBenefitsByMembershipId(@Param("membership_id") membershipId: Int): List<MembershipBenefitEntity>

    // 插入新的会员权益
    @Insert("""
        INSERT INTO membership_benefits (benefit_type, benefit_name, benefit_value, description) 
        VALUES (#{benefitType}, #{benefitName}, #{benefitValue}, #{description})
    """)
    fun insertBenefit(benefit: MembershipBenefitEntity): Int

    // 更新会员权益
    @Update("""
        UPDATE membership_benefits SET 
            benefit_type = #{benefitType}, 
            benefit_name = #{benefitName}, 
            benefit_value = #{benefitValue}, 
            description = #{description}
        WHERE id = #{id}
    """)
    fun updateBenefit(benefit: MembershipBenefitEntity): Int

    // 删除会员权益
    @Delete("DELETE FROM membership_benefits WHERE id = #{id}")
    fun deleteBenefitById(id: Int): Int

    // 查询所有会员权益
    @Select("SELECT * FROM membership_benefits")
    fun getAllBenefits(): List<MembershipBenefitEntity>
}
