package com.xfposthouse

import org.mybatis.spring.annotation.MapperScan
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.runApplication
import org.springframework.context.annotation.ComponentScan

@SpringBootApplication(scanBasePackages = ["com.xfposthouse", "discipline"])
@MapperScan("com.xfposthouse.mapper", "mapper")
class XfPosthouseApplication

fun main(args: Array<String>) {
    runApplication<XfPosthouseApplication>(*args)
    println("启动成功")
}
