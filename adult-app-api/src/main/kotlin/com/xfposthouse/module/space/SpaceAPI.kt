package com.xfposthouse.module.space

import com.xfposthouse.jwt.JWTManager
import com.xfposthouse.mapper.FamilyMapper
import com.xfposthouse.mapper.FolderMapper
import com.xfposthouse.mapper.SpaceMapper
import com.xfposthouse.module.family.FamilyApi
import com.xfposthouse.network.entity.XFErrorCode
import com.xfposthouse.network.entity.XFResponseEntity
import com.xfposthouse.module.space.entity.FolderEntity
import com.xfposthouse.module.space.entity.SpaceEntity
import com.xfposthouse.upload.FileUploadController
import com.xfposthouse.user.api.XFUserAPI
import com.xfposthouse.util.CommonUtil
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api")
class SpaceAPI {

    @Autowired
    lateinit var spaceMapper: SpaceMapper

    @Autowired
    lateinit var folderMapper: FolderMapper

    @Autowired
    lateinit var fileAPI: FileUploadController

    @Autowired
    lateinit var userAPI: XFUser<PERSON>I

    @Autowired
    lateinit var familyMapper: FamilyMapper

    @PostMapping("/space/imageList")
    fun getImages(@RequestBody param: Map<String, Any>, @RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            val userId = JWTManager.instance.verifyToken(token)
            val pageSize = param["pageSize"] as? Int ?: 20
            val pageIndex = param["pageIndex"] as? Int ?: 1
            val folderId = param["folderId"] as? Int ?: 0
            val resultMap = mutableMapOf<String, Any?>()
            val list: List<SpaceEntity>?
            if (folderId > 0) {
                list = spaceMapper.getSpaceEntityByFolderIdAndUserId(userId.toLong(), folderId, pageSize, (pageIndex - 1) * pageSize)
                val pair = spaceMapper.getTotalCountAndFileSize(folderId)
                resultMap["totalCount"] = pair.first
                resultMap["totalFileSize"] = pair.second
            } else {
                list = spaceMapper.getUserSpaceEntities(userId.toInt(), pageSize, (pageIndex - 1) * pageSize)
            }
            resultMap["list"] = list
            result.result = resultMap
        } catch (e: Error) {
            result.setError(XFErrorCode.PARAM_INVALID)
        }
        return result
    }

    @PostMapping("/space/createImageFile")
    fun createImageFile(@RequestBody param: Map<String, Any>, @RequestHeader("token") token: String, @RequestParam(required = false) pair: Pair<Long, Long>?): XFResponseEntity {
        val result = XFResponseEntity()
        try {
//            val userId = param["authorId"] as? Int ?: 0
            val userId = JWTManager.instance.verifyToken(token).toInt()
            if (userId > 0) {
                // 本次上传文件总大小
                val totalFileSize = (param["totalFileSize"] as? Number)?.toLong() ?: 10995116277760
                val userSpacePair = pair ?: userAPI.getUserSpaceUsedInfo(userId)
                // 用户总私有云空间大小
                val totalSpaceSize = userSpacePair.second
                // 已使用云空间大小
                val usedSpaceSize = userSpacePair.first
                if (totalFileSize + usedSpaceSize > totalSpaceSize) {
                    result.setError(XFErrorCode.USER_NO_SPACE)
                    return result
                }
                val folderId = param["folderId"] as? Int ?:0
                val images = param["imageObjects"] as? List<Map<String, Any>>
                images?.forEach { imageJson ->
                    val spaceEntity = SpaceEntity.fromJson(imageJson)
                    if (folderId > 0) {
                        spaceEntity.groupId = folderId
                    }
                    spaceEntity.userId = userId
                    spaceEntity.createTime = System.currentTimeMillis()
                    val blogId = param["blogId"] as? Long ?: 0
                    if (blogId > 0) {
                        spaceEntity.blogId = param["blogId"] as? Long ?: 0
                    }
                    spaceMapper.insertSpaceEntity(spaceEntity)
                }
                userAPI.updateUsedSpace(userId, usedSpaceSize + totalFileSize)
                result.result = mapOf("usedSize" to usedSpaceSize + totalFileSize)
            } else {
                result.setError(XFErrorCode.PARAM_INVALID)
            }

        } catch (e: Error) {
            result.setError(XFErrorCode.PARAM_INVALID)
        }
        return result
    }

    @PostMapping("/space/createImageFolder")
    fun createImageFolder(@RequestBody param: Map<String, Any>, @RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            val name = param["name"] as? String
            val userId = JWTManager.instance.verifyToken(token).toInt()
            if (name != null && userId > 0) {
                val folderEntity = FolderEntity.fromJson(param)
                folderEntity.createTime = System.currentTimeMillis()
                folderEntity.userId = userId
                folderMapper.insertFolder(folderEntity)
            } else if (userId <= 0) {
                result.setError(XFErrorCode.INVALID_TOKEN)
            } else {
                result.setError(XFErrorCode.PARAM_INVALID)
            }

        } catch (e: Error) {
            result.setError(XFErrorCode.PARAM_INVALID)
        }
        return result
    }

    @PostMapping("/space/getFolderList")
    fun getFolderList(@RequestBody param: Map<String, Any>, @RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            val userId = JWTManager.instance.verifyToken(token).toInt()
            if (userId > 0) {
                val list = folderMapper.findByUserId(userId)
                result.result = mutableMapOf("list" to list)
            } else {
                result.setError(XFErrorCode.INVALID_TOKEN)
            }

        } catch (e: Error) {
            result.setError(XFErrorCode.PARAM_INVALID)
        }
        return result
    }

    @PostMapping("/space/deleteFiles")
    fun deleteImages(@RequestBody param: Map<String, Any>, @RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            val userId = JWTManager.instance.verifyToken(token).toInt()
            if (userId > 0) {
                val list = param["list"] as? List<Map<String, Any>>
                val ids = list?.map { json -> (json["id"] as? Int ?: 0).toString().toLong() }
                if (ids.isNullOrEmpty()) {
                    result.setError(XFErrorCode.INVALID_TOKEN)
                } else {
                    spaceMapper.deleteSpaceEntities(ids)
                    updateUsedSpace(userId)
                    val fileKeys = list.map { json -> json["fileKey"] as? String ?: "" }
                    fileAPI.deleteFiles(fileKeys)
                    result.result = mapOf("usedSize" to spaceMapper.getUserUsedSpace(userId))
                }

            } else {
                result.setError(XFErrorCode.INVALID_TOKEN)
            }

        } catch (e: Error) {
            result.setError(XFErrorCode.PARAM_INVALID)
        }
        return result
    }

    @PostMapping("/space/moveToFolder")
    fun moveToFolder(@RequestBody params: Map<String, Any>, @RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            val userId = JWTManager.instance.verifyToken(token).toInt()
            if (userId > 0) {
                val ids = params["list"] as List<Int>
                val folderId = params["folderId"] as? Int ?: 0
                spaceMapper.updateGroupIdByIds(ids, folderId)

            } else {
                result.setError(XFErrorCode.INVALID_TOKEN)
            }
        } catch (e: Error) {
            result.setError(XFErrorCode.PARAM_INVALID)
        }
        return result
    }

    @PostMapping("/space/getFamilyFolderList")
    fun getFamilyFolderList(@RequestBody param: Map<String, Any>, @RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            val userId = JWTManager.instance.verifyToken(token).toInt()
            val familyId = param["familyId"] as? Int ?: 0
            if (userId > 0) {
                val userIds = familyMapper.findMembersById(familyId)
                if (userIds.contains(userId)) {
                    val idsStr = CommonUtil.listToString(userIds)
                    val list = folderMapper.getFoldersByUserIds(idsStr)
                    result.result = mapOf("list" to list)
                } else {
                    result.setError(XFErrorCode.PARAM_INVALID)
                }
            } else {
                result.setError(XFErrorCode.INVALID_TOKEN)
            }

        } catch (e: Error) {
            result.setError(XFErrorCode.PARAM_INVALID)
        }
        return result
    }

    @PostMapping("/space/getFamilyImages")
    fun getFamilyImages(@RequestBody param: Map<String, Any>, @RequestHeader("token") token: String) : XFResponseEntity {
        val result = XFResponseEntity()
        try {
            val userId = JWTManager.instance.verifyToken(token).toInt()
            val familyId = param["familyId"] as? Int ?: 0
            val pageSize = param["pageSize"] as? Int ?: 20
            val pageIndex = param["pageIndex"] as? Int ?: 1
            if (userId > 0) {
                val userIds = familyMapper.findMembersById(familyId)
                if (userIds.contains(userId)) {
                    val idsStr = CommonUtil.listToString(userIds)
                    /// 获取文件夹列表
                    val list = folderMapper.getFoldersByUserIds(idsStr)
                    /// 获取图片列表
                    val images = spaceMapper.getUsersSpaceEntities(idsStr, pageSize, (pageIndex - 1) * pageSize)
                    val resultMap = mutableMapOf<String, Any?>()
                    resultMap["folders"] = list
                    resultMap["images"] = images
                    result.result = resultMap
                } else {
                    result.setError(XFErrorCode.PARAM_INVALID)
                }
            } else {
                result.setError(XFErrorCode.INVALID_TOKEN)
            }
        } catch (e: Error) {
            result.setError(XFErrorCode.PARAM_INVALID)
        }
        return result
    }

    /// 获取用户空间使用情况
    fun getUserSpaceUsedInfo(userId: Int): Pair<Long, Long> {
        return userAPI.getUserSpaceUsedInfo(userId)
    }

    fun updateUsedSpace(userId: Int) {
        val userSpace = spaceMapper.getUserUsedSpace(userId)
        userAPI.updateUsedSpace(userId, userSpace)
    }

}