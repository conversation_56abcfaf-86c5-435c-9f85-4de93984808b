package com.xfposthouse.mapper

import com.xfposthouse.module.life_record.entity.LifeRecordEntity
import com.xfposthouse.user.entity.XFUserEntity
import org.apache.ibatis.annotations.*
import org.springframework.stereotype.Repository
import com.mybatisflex.core.BaseMapper

@Repository
interface LifeRecordMapper : BaseMapper<LifeRecordEntity> {

    @Select("SELECT * FROM life_record ORDER BY create_time DESC LIMIT #{pageSize} OFFSET #{offset}")
    @Results(
        id = "lifeRecordMap", value = [
            Result(property = "id", column = "id"),
            Result(property = "title", column = "title"),
            Result(property = "description", column = "description"),
            Result(property = "createTime", column = "create_time"),
            Result(property = "publishTime", column = "publish_time"),
            Result(property = "fileNames", column = "file_names", typeHandler = StringToArrayTypeHandler::class),
            Result(property = "imageUrls", column = "image_urls", typeHandler = StringToArrayTypeHandler::class),
            Result(property = "videoUrl", column = "video_url"),
            Result(property = "videoFileName", column = "video_file_name"),
            Result(property = "isPublic", column = "is_public"),
            Result(property = "status", column = "status"),
            Result(property = "locationTitle", column = "location_title"),
            Result(property = "location", column = "location"),
            Result(property = "authorInfo", column = "author_id", javaType = Map::class, one = One(select = "com.xfposthouse.mapper.UserMapper.getSmallUserByUserId")),
        ]
    )
    fun selectLifeRecords(pageSize: Int, offset: Int): List<LifeRecordEntity>

    // 查询所有生活记录，并将 users 表的信息联接进来
    @Select("SELECT lr.* FROM life_record lr JOIN users u ON lr.author_id = u.uid WHERE lr.status = 2 AND lr.is_public = true AND u.userState = 1 ORDER BY lr.create_time DESC LIMIT #{pageSize} OFFSET #{offset}")
    @ResultMap("lifeRecordMap")
    fun getLifeRecords(pageSize: Int, offset: Int): List<LifeRecordEntity>

    @Select("<script>" +
            "SELECT lr.* FROM life_record lr " +
            "JOIN users u ON lr.author_id = u.uid " +
            "WHERE lr.status = 2 " +
            "AND lr.author_id NOT IN " +
            "<foreach item='userId' collection='userIds' open='(' separator=',' close=')'>#{userId}</foreach> " +
            "AND u.userState = 1 " +  // 1表示正常状态
            "ORDER BY lr.create_time DESC " +
            "LIMIT #{pageSize} OFFSET #{offset}" +
            "</script>")
    @ResultMap("lifeRecordMap")
    fun getLifeRecordsByUsers(
            @Param("userIds") userIds: List<Int>,
            @Param("pageSize") pageSize: Int,
            @Param("offset") offset: Int
    ): List<LifeRecordEntity>

    @Insert(
        """
        INSERT INTO life_record (title, description, create_time, file_names, image_urls, video_url, video_file_name, author_id, location, location_title)
        VALUES (#{lifeRecord.title}, #{lifeRecord.description}, #{lifeRecord.createTime}, #{fileNames}, #{imageUrls}, #{lifeRecord.videoUrl}, 
        #{lifeRecord.videoFileName}, #{lifeRecord.authorInfo.userId}, #{lifeRecord.location}, #{lifeRecord.locationTitle})
    """
    )
    @Options(useGeneratedKeys = true, keyProperty = "lifeRecord.id")
    fun insertLifeRecord(lifeRecord: LifeRecordEntity, fileNames: String, imageUrls: String): Int

    @Select("SELECT * FROM life_record WHERE id = #{id}")
    @ResultMap("lifeRecordMap")
    fun selectLifeRecordById(id: Long): LifeRecordEntity?

    @Update("UPDATE life_record SET status = #{status} WHERE id = #{id}")
    fun updateLifeRecordStatus(id: Int, status: Int): Int

    @Delete("DELETE FROM life_record WHERE id = #{id}")
    fun deleteLifeRecord(id: Int): Int

    @Update(
        """
        UPDATE life_record
        SET
            title = IFNULL(#{title}, title),
            description = IFNULL(#{description}, description),
            is_public = IFNULL(#{isPublic}, is_public),
            status = IFNULL(#{status}, status)
        WHERE id = #{id}
        """
    )
    fun updateLifeRecord(lifeRecord: LifeRecordEntity): Int

    // 批量删除
    @Delete(
        "<script>" +
                "DELETE FROM life_record WHERE id IN " +
                "<foreach item='id' collection='list' open='(' separator=',' close=')'>" +
                "#{id}" +
                "</foreach>" +
                "</script>"
    )
    fun deleteLifeRecords(@Param("list") ids: List<Int?>?): Int

    @Select(
        """
        SELECT * FROM (
    (
        SELECT * 
        FROM life_record 
        WHERE 
            author_id = #{authorId} 
            AND create_time <= #{createTime} 
        ORDER BY create_time DESC 
        LIMIT 5
    ) 
    UNION ALL 
    (
        SELECT * 
        FROM life_record 
        WHERE 
            author_id = #{authorId} 
            AND create_time > #{createTime} 
        ORDER BY create_time ASC 
        LIMIT 5
    )
) AS combined_results 
ORDER BY create_time ASC;
        """
    )
    @ResultMap("lifeRecordMap")
    fun getRecordsByCreateTime(authorId: Int, createTime: Long): List<LifeRecordEntity>

    @Select("SELECT * FROM life_record WHERE author_id = #{authorId} AND create_time < #{createTime} ORDER BY create_time DESC LIMIT #{pageSize}")
    @ResultMap("lifeRecordMap")
    fun getFrontRecords(authorId: Int, createTime: Long, pageSize: Int) : List<LifeRecordEntity>

    @Select("SELECT * FROM life_record WHERE author_id = #{authorId} AND create_time > #{createTime} ORDER BY create_time DESC LIMIT #{pageSize}")
    @ResultMap("lifeRecordMap")
    fun getLastRecords(authorId: Int, createTime: Long, pageSize: Int) : List<LifeRecordEntity>

    @Select("SELECT COUNT(*) FROM life_record")
    fun getRecordCount(): Int
}
