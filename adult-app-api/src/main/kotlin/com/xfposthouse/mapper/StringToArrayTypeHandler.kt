package com.xfposthouse.mapper

import com.fasterxml.jackson.databind.ObjectMapper
import org.apache.ibatis.type.BaseTypeHandler
import org.apache.ibatis.type.JdbcType
import java.sql.CallableStatement
import java.sql.PreparedStatement
import java.sql.ResultSet

class StringToArrayTypeHandler: BaseTypeHandler<List<String>?>() {
    private val objectMapper = ObjectMapper()

    override fun setNonNullParameter(ps: PreparedStatement, i: Int, parameter: List<String>?, jdbcType: JdbcType?) {
        ps.setString(i, objectMapper.writeValueAsString(parameter))
    }

    override fun getNullableResult(rs: ResultSet, columnName: String): List<String>? {
        rs.getString(columnName)?.let {
            if (it.isEmpty()) {
                return null
            }
            return objectMapper.readValue(it, List::class.java) as List<String>?
        }
        return null
    }

    override fun getNullableResult(rs: ResultSet, columnIndex: Int): List<String>? {
        rs.getString(columnIndex)?.let {
            if (it.isEmpty()) {
                return null
            }
            return objectMapper.readValue(it, List::class.java) as List<String>?
        }
        return null
    }

    override fun getNullableResult(cs: CallableStatement, columnIndex: Int): List<String>? {
        cs.getString(columnIndex)?.let {
            if (it.isEmpty()) {
                return null
            }
            return objectMapper.readValue(it, List::class.java) as List<String>?
        }
        return null
    }
}