package com.xfposthouse.mapper

import com.xfposthouse.user.entity.XFUserEntity
import org.apache.ibatis.annotations.*
import org.springframework.stereotype.Repository
import com.mybatisflex.core.BaseMapper

@Repository
interface UserMapper : BaseMapper<XFUserEntity> {

    @Select("SELECT * FROM users WHERE uid = #{userId}")
    @Results(
        id = "userMap", value = [
            Result(property = "userId", column = "uid"),
            Result(property = "nickName", column = "nick_name"),
            Result(property = "userName", column = "user_name"),
            Result(property = "level", column = "level"),
//            Result(property = "passWord", column = "password"),
            Result(property = "role", column = "role"),
            Result(property = "registerTime", column = "register_time"),
            Result(property = "sex", column = "sex"),
            Result(property = "tags", column = "tags", typeHandler = StringToArrayTypeHandler::class),
            Result(property = "chatCount", column = "chatCount"),
            Result(property = "fiveStarCommentCount", column = "fiveStarCommentCount"),
            Result(property = "badCommentCount", column = "badCommentCount"),
            Result(property = "isVerified", column = "isVerified"),
            Result(property = "avatar", column = "avatar"),
            Result(property = "introduction", column = "introduction"),
            Result(property = "userState", column = "userState"),
            Result(property = "usedSpaceSize", column = "used_space_size"),
            Result(property = "totalSpaceSize", column = "total_space_size"),
            Result(property = "totalVoiceCount", column = "total_voice_count"),
            Result(property = "usedVoiceCount", column = "used_voice_count"),
            Result(property = "membershipId", column = "membership_id"),
            Result(property = "membershipStartTime", column = "membership_start_time"),
            Result(property = "membershipEndTime", column = "membership_end_time"),
            Result(property = "membershipLevel", column = "membership_level"),
            Result(property = "birthDay", column = "birth_day"),
            Result(property = "familyId", column = "family_id"),
            Result(property = "phoneNumber", column = "phone_number")
        ]
    )
    fun getUserByUserId(userId: Int): XFUserEntity?

    @Select("SELECT uid, nick_name, user_name, avatar  FROM users WHERE uid = #{userId}")
    @Results(value = [
        Result(property = "userId", column = "uid"),
        Result(property = "nickName", column = "nick_name"),
        Result(property = "userName", column = "user_name"),
    ])
    fun getSmallUserByUserId(userId: Int): Map<String, Any>?

    @Select("SELECT * FROM users WHERE user_name = #{userName}")
    @ResultMap("userMap")
    fun getUserByUserName(userName: String): XFUserEntity?

    @Select("SELECT password FROM users WHERE user_name = #{userName}")
    fun getUserPassword(userName: String): String?

    @Insert("""
        INSERT INTO users (
            user_name,
            password,
            nick_name,
            total_space_size,
            total_voice_count,
            avatar,
            register_time
        ) VALUES (
            #{user.userName},
            #{pwd},
            #{user.nickName},
            #{user.totalSpaceSize},
            #{user.totalVoiceCount},
            #{user.avatar},
            #{user.registerTime}
        )
    """)
    @Options(useGeneratedKeys = true, keyProperty = "user.userId")
    fun insertUser(user: XFUserEntity, pwd: String): Int

    @Update("""
        UPDATE users SET 
            nick_name = COALESCE(#{nickName}, nick_name),
            level = COALESCE(#{level}, level),
            role = COALESCE(#{role}, role),
            sex = COALESCE(#{sex}, sex),
            chatCount = COALESCE(#{chatCount}, chatCount),
            fiveStarCommentCount = COALESCE(#{fiveStarCommentCount}, fiveStarCommentCount),
            badCommentCount = COALESCE(#{badCommentCount}, badCommentCount),
            isVerified = COALESCE(#{isVerified}, isVerified),
            avatar = COALESCE(#{avatar}, avatar),
            introduction = COALESCE(#{introduction}, introduction),
            userState = COALESCE(#{userState}, userState),
            used_space_size = COALESCE(#{usedSpaceSize}, used_space_size),
            total_space_size = COALESCE(#{totalSpaceSize}, total_space_size),
            total_voice_count = COALESCE(#{totalVoiceCount}, total_voice_count),
            used_voice_count = COALESCE(#{usedVoiceCount}, used_voice_count),
            membership_id = COALESCE(#{membershipId}, membership_id),
            membership_start_time = COALESCE(#{membershipStartTime}, membership_start_time),
            membership_end_time = COALESCE(#{membershipEndTime}, membership_end_time),
            membership_level = COALESCE(#{membershipLevel}, membership_level),
            birth_day = COALESCE(#{birthDay}, birth_day),
            phone_number = COALESCE(#{phoneNumber}, phone_number)
            WHERE uid = #{userId}
    """)
    fun updateUser(user: XFUserEntity)


//    @Select(
//        """
//        SELECT uid, nick_name, avatar FROM users WHERE uid IN
//        <foreach item="item" collection="list" open="(" separator="," close=")">
//            #{item}
//        </foreach>
//        """
//    )
    @Select("<script>" +
            "SELECT uid, nick_name, avatar FROM users WHERE uid IN " +
            "<foreach item='userId' collection='userIds' open='(' separator=',' close=')'>#{userId} </foreach> " +
            "</script>")
    @Results(value = [
        Result(property = "userId", column = "uid"),
        Result(property = "nickName", column = "nick_name"),
        Result(property = "avatar", column = "avatar"),
    ])
    fun getUserListByIds(@Param("userIds") userIds: List<Int>): List<Map<String, Any>>

    @Select("SELECT  * FROM users ORDER BY register_time DESC LIMIT #{pageSize} OFFSET #{offset}")
    @ResultMap("userMap")
    fun getUserList(
        @Param("pageSize") pageSize: Int,
        @Param("offset") offset: Int,
    ): List<XFUserEntity>

    @Select("SELECT uid, nick_name, avatar, role FROM users WHERE uid = #{userId}")
    @Results(value = [
        Result(property = "userId", column = "uid"),
        Result(property = "nickName", column = "nick_name"),
        Result(property = "avatar", column = "avatar"),
        Result(property = "role", column = "role"),
    ])
    fun getShortUserInfo(userId: Int): Map<String, Any>?

    @Update("UPDATE users SET password = #{pwd} WHERE uid = #{userId}")
    fun updatePassWord(userId: Int, pwd: String)

    @Update("UPDATE users SET used_space_size = #{usedSpaceSize} WHERE uid = #{userId}")
    fun updateUsedSpace(userId: Int, usedSpaceSize: Long)

    @Update("UPDATE users SET used_voice_count = #{usedVoiceCount} WHERE uid = #{userId}")
    fun updateUsedVoiceCount(userId: Int, usedVoiceCount: Int)

    /// 用户状态, 1，正常，0，禁用 3，注销中
    @Update("UPDATE users SET userState = #{userState} WHERE uid = #{userId}")
    fun updateUserState(userId: Int, userState: Int)

    @Select("SELECT used_space_size, total_space_size FROM users WHERE uid = #{userId}")
    @Results(
        Result(property = "usedSpace", column = "used_space_size"),
        Result(property = "totalSpace", column = "total_space_size")
    )
    fun getUserSpaceUsage(userId: Int): Map<String, Any>?

    // 更新会员信息（包含会员等级）
    @Update("""
        UPDATE users SET 
            membership_id = #{membershipId},
            membership_start_time = #{membershipStartTime},
            membership_end_time = #{membershipEndTime},
            membership_level = #{membershipLevel}
        WHERE uid = #{userId}
    """)
    fun updateUserMembership(userId: Int, membershipId: Int?, membershipStartTime: Long?, membershipEndTime: Long?, membershipLevel: Int)

    // 查询用户是否存在
    @Select("SELECT COUNT(*) FROM users WHERE uid = #{id}")
    fun checkUserExistsById(@Param("id") id: Int): Int

    /// 获取用户家庭id
    @Select("SELECT family_id FROM users WHERE uid = #{userId}")
    fun getFamilyId(userId: Int): Int?

    /// 插入家庭id
    @Select("UPDATE users SET family_id = #{familyId} WHERE uid = #{userId}")
    fun updateFamilyId(familyId: Int, userId: Int)

    @Select("SELECT COUNT(*) FROM users")
    fun getUsersCount(): Int

    @Select("SELECT role FROM users WHERE uid = #{userId}")
    fun getUserRole(userId: Int): Int
}
