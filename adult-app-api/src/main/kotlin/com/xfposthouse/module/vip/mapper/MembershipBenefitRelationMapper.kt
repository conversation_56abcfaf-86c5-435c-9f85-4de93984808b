package com.xfposthouse.module.vip.mapper

import com.xfposthouse.module.vip.entity.MembershipBenefitEntity
import com.xfposthouse.module.vip.entity.MembershipBenefitRelationEntity
import org.apache.ibatis.annotations.*
import org.springframework.stereotype.Repository
import com.mybatisflex.core.BaseMapper

@Repository
interface MembershipBenefitRelationMapper: BaseMapper<MembershipBenefitRelationEntity> {

    @Select("""
        SELECT 
            b.id AS id, 
            b.benefit_type, 
            b.benefit_name, 
            b.benefit_value, 
            b.description,
            b.created_at, 
            b.updated_at
        FROM membership_benefit_relations r
        INNER JOIN membership_benefits b ON r.benefit_id = b.id
        WHERE r.membership_id = #{membershipId}
    """)
    @Results(id = "benefitMap", value = [
        Result(property = "id", column = "id"),
        Result(property = "benefitType", column = "benefit_type"),
        Result(property = "benefitName", column = "benefit_name"),
        Result(property = "benefitValue", column = "benefit_value"),
        Result(property = "description", column = "description"),
        Result(property = "createdAt", column = "created_at"),
        Result(property = "updatedAt", column = "updated_at")
    ])
    fun findBenefitsByMembershipId(membershipId: Int): List<MembershipBenefitEntity>

    // 根据权益ID查询所有的会员
    @Select("SELECT * FROM membership_benefit_relations WHERE benefit_id = #{benefitId}")
    fun getMembershipsByBenefitId(benefitId: Int): List<MembershipBenefitRelationEntity>

    // 根据会员ID和权益ID查询该关系是否存在
    @Select("SELECT * FROM membership_benefit_relations WHERE membership_id = #{membershipId} AND benefit_id = #{benefitId}")
    fun getRelationByMembershipIdAndBenefitId(membershipId: Int, benefitId: Int): MembershipBenefitRelationEntity?

    // 插入一条会员权益关系记录
    @Insert("""
        INSERT INTO membership_benefit_relations (membership_id, benefit_id) 
        VALUES (#{membershipId}, #{benefitId})
    """)
    fun insertRelation(relation: MembershipBenefitRelationEntity): Int

    // 删除一条会员权益关系记录
    @Delete("DELETE FROM membership_benefit_relations WHERE membership_id = #{membershipId} AND benefit_id = #{benefitId}")
    fun deleteRelationByIds(membershipId: Int, benefitId: Int): Int

    // 查询所有会员与权益的关系
    @Select("SELECT * FROM membership_benefit_relations")
    fun getAllRelations(): List<MembershipBenefitRelationEntity>
}
