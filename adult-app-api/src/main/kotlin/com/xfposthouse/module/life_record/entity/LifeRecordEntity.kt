package com.xfposthouse.module.life_record.entity


open class LifeRecordEntity {

    var id: Long? = null
    var title: String? = null
    var description: String? = null
    var createTime: Long? = null
    var publishTime: Long? = null
    var fileNames: List<String>? = null
    var imageUrls: List<String>? = null
    var videoUrl: String? = null
    var videoFileName: String? = null
    var authorInfo: Map<String, Any>? = null
    var isPublic: Boolean? = null
    // 1，审核中 2，审核通过 3, 审核不通过
    var status: Int? = null

    var isLiked: Boolean? = null
    var likedCount: Int? = null
    var commentCount: Int? = null

    /// 定位信息
    var locationTitle: String? = null
    var location: String? = null

    companion object {
        fun fromJson(json: Map<String, Any?>): LifeRecordEntity {
            val lifeRecordEntity = LifeRecordEntity()
            lifeRecordEntity.id = json["id"] as Long?
            lifeRecordEntity.title = json["title"] as String?
            lifeRecordEntity.description = json["description"] as String?
            lifeRecordEntity.createTime = json["createTime"] as Long?
            lifeRecordEntity.publishTime = json["publishTime"] as Long?
            lifeRecordEntity.fileNames = json["fileNames"] as List<String>?
            lifeRecordEntity.isPublic = json["isPublic"] as Boolean?
            lifeRecordEntity.status = json["status"] as Int?
            lifeRecordEntity.videoUrl = json["videoUrl"] as String?
            lifeRecordEntity.videoFileName = json["videoFileName"] as String?
            lifeRecordEntity.location = json["location"] as String?
            lifeRecordEntity.locationTitle = json["locationTitle"] as String?
            if (json["authorId"] != null) {
                val user = mutableMapOf<String, Any>()
                user["userId"] = json["authorId"] as Int
                lifeRecordEntity.authorInfo = user
            }
            if (json["imageObjects"] != null) {
                val images = json["imageObjects"] as List<Map<String, Any?>>?
                val imageObjects = ArrayList<String>()
                images?.forEach { imageJson ->
                    val imageUrl = imageJson["fileUrl"] as String
                    imageObjects.add(imageUrl)
                }
                lifeRecordEntity.imageUrls = imageObjects
            }
            return lifeRecordEntity
        }
    }
}

open class LifeReportEntity {

    var id: Int? = null
    var title: String? = null
    var description: String? = null
    var createTime: Long? = null
    var imageUrls: List<String>? = null
    var authorInfo: Map<String, Any>? = null
    var blogId: Long? = null
    companion object {
        fun fromJson(json: Map<String, Any?>): LifeReportEntity {
            val lifeReportEntity = LifeReportEntity()
            lifeReportEntity.id = json["id"] as Int?
            lifeReportEntity.title = json["title"] as String?
            lifeReportEntity.description = json["description"] as String?
            lifeReportEntity.createTime = json["createTime"] as Long?
            lifeReportEntity.blogId = json["blogId"].toString().toLong()
            if (json["authorId"] != null) {
                val user = mutableMapOf<String, Any>()
                user["userId"] = json["authorId"] as Int
                lifeReportEntity.authorInfo = user
            }
            if (json["imageObjects"] != null) {
                val images = json["imageObjects"] as List<Map<String, Any?>>?
                val imageObjects = ArrayList<String>()
                images?.forEach { imageJson ->
                    val imageUrl = imageJson["fileUrl"] as String
                    imageObjects.add(imageUrl)
                }
                lifeReportEntity.imageUrls = imageObjects
            }
            return lifeReportEntity
        }
    }
}

data class ImageObject(
    var url: String? = null,
    var width: Int? = null,
    var height: Int? = null,
    var size: Long? = null
) {
    companion object {
        fun fromJson(json: Map<String, Any?>): ImageObject {
            val imageObject = ImageObject()
            imageObject.url = json["url"] as String?
            imageObject.width = json["width"] as Int?
            imageObject.height = json["height"] as Int?
            imageObject.size = json["size"] as Long?
            return imageObject
        }
    }
}
