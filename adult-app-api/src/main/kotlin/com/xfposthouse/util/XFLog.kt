package com.xfposthouse.util

class XFLog  {

    companion object {

        private var _isDebug = false

        fun debugLog(message: String) {
            if (isDebug()) {
                println(message)
            }
            println(message)
        }

        private fun isDebug(): Boolean {
            if (_isDebug) {
                return true
            }
            val debugFlag = System.getProperty("debug")
            _isDebug = "true".equals(debugFlag, ignoreCase = true)
            return _isDebug
        }
    }

}