package com.xfposthouse.pay

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.alipay.api.AlipayClient
import com.alipay.api.DefaultAlipayClient
import com.alipay.api.domain.AlipayTradeAppPayModel
import com.alipay.api.domain.GoodsDetail
import com.alipay.api.request.AlipayTradeAppPayRequest
import com.alipay.api.request.AlipayTradeWapPayRequest
import com.xfposthouse.jwt.JWTManager
import com.xfposthouse.mapper.PayOrderMapper
import com.xfposthouse.network.entity.XFErrorCode
import com.xfposthouse.network.entity.XFResponseEntity
import com.xfposthouse.pay.entity.PayOrderEntity
import com.xfposthouse.module.vip.MemberShipApi
import com.google.gson.Gson
import com.wechat.pay.java.core.Config
import com.wechat.pay.java.core.RSAAutoCertificateConfig
import com.wechat.pay.java.service.payments.h5.H5Service
import com.wechat.pay.java.service.payments.h5.model.Amount
import com.wechat.pay.java.service.payments.h5.model.PrepayRequest
import com.wechat.pay.java.service.payments.h5.model.PrepayResponse
import jakarta.servlet.http.HttpServletRequest
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter


@RestController
@RequestMapping("/api")
class XFAliPayApi() {

    @Autowired
    private lateinit var gson: Gson

    /// 应用id
    val appId: String = "2021005120643584"

    /// 私钥
    val privateKey: String = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQChgQK9P+e0yfglQiIUl99G9+vAJUAHXhkSvq+ePXwd09cnNIOyLx5Edr6s2FX4X5tNCaepTLEy1W4nhhoPPzqig9Fq1drzfavDJ8JczJQbc87L1IgM29z4Jm9lor8nC4gsT8VWwMMVisQx5TI82zXsAdkfA/a5zF2DHiMX1v507wCFfOi8k/johjEhY90P24oBnrRqa+awnWdPhfzSSLukggsutqjSAOQGKjxEz5xCi7DxOLDD8HqBRamaEubIeEnnag+eoK7uY3NBpBvCDS/jkzxI4Lw+XUqYYJbi85kYdA8HkBvoYrRDui8/U5haiddhDbkQhO3FERYu6fsEHp8XAgMBAAECggEAIBP40FAWHKGVCQl2rXJw5a6ug66iZhQ5lLCNPzflJCOEc4QJuGWFdIHFY51gjgGnXgjR5V2XQIqF45HOMUMUyVy3RqoNYBmQ+SU3hKkZrc41v+SJ1UwCOsOfkqddokq3FF4PyyhkVy4FxAdLEC1WK3c+gduOsN63oChnPf/08JKLTvT6tF8aMNW0idbVCGu+K5gYep4rFZ1yl+58G4tC75VtHOpOGRzIX57Gar7h8hiOv5baZtB4aE3mpmZGr3PXHcHzYjXUv1vZPwiWmy2PhMxjRYz+xHbnJDpKkwbGcm0UdYbObkszkvOZQyt3ac8UyOUS/92mOTQWCxJOZe0qwQKBgQDRsy/JgpVgKqpadmh7R9njqRsuDuy4rTJlpt/5oyqmT0CBemo02UYCs5Gg10SL3v6QnfL3DklnIC6tqcaphnmbb8140CtI3dNrq2P/h1uNoKSZc+Zjbee41wyiJavQ7e66Bq5XYgCHFEzs2PUZzqsa16/vckQq41tUSLOt0FrjIQKBgQDFKaeTRa4mNGQt/xu8a2upJsWQVYFjZIpzAnHRCJ9rylgAzGbTXbNbpBJmzz2um2T9ZP/y0/ETo6y/AXLSztEkZWEiW4DOX1/kwnvdTQAM7QzNoK3ggTWaCEG/pysUO4WmIsgpoP/nuEh/kUl1kq9E8cx5JMeD1V95qSj/apBzNwKBgQC6Ay7Cm1kRwwb1087+fE/lSJ5ZjtehQdFQpmRLNtegSvC3afVIB+u4fb0SZaHiSN6bPeDGEiA+oCeH2O/mdOjuXpBvOIkMy+i8u32qr1EhInyfulWFoXpJVklYk/4DCOwYi9Cepso9CF7sQc+5IsBYwTbJOh0Jxj+/VfY8mnPkwQKBgCZ9XUN45Oh8bdo05TtBVUEuWn800g+PPx/4qDOAlW67j7aIMMKulicbvqPnbUk6+Nom73h+O5/z4fNf2wSgb1/FktLcPicLo3LLLNclAw+C727tIUSktMwfNIavoc9PV2X5gE3wKkUYSr7spASZ4DF7aslzjqeD7iGYDnLeYNi9AoGAHEvhkdhhh7s9ID+1oB/Zx4BzUXou8SKPZPvmnIBLvfCjhP+8Vec7DLKAtr+W7eSHmJU2omqwl3yNpqDlGRfFthnSrTQtCSPGvZNV0htdz0ScRh9Z1AcCUzxS7oKpihDMWuWbYEKcLHR4+L1gORiMc7TdyF7V1yw9jFxN93MfcvY="

    /// 公钥
    val publicKey: String =  "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAs6uxBIGee1mBl+F+tjJW5r/DP84euVI//akuDLD7MuMuc2mrz0rNVD/BCY3EtC8D8krADyWa7bSQfWZ1NNvFO7XEo1/agXoogBQmvWOtm5oqggmFxTzhAZyL0bLEShvNkz5o8SS2oh+qdMK0krE0+OPY8YNrzRm2EVdpFbdlK0ZtE60es1WA680+n3JJe3IIO7InCEfZQbIW7kphaQ6WQhYu+aVmts7dY8oBr15NAfRyv0t9t0X7T+QD5X76l4aJ65XPdfgrafFyd1k+n+4QsPnhwc9fnSCDzfP3hvtyBkmr5iMGq9vZcXK65SlCswLGxzIjau/EA0llPfDwssi+cwIDAQAB"

    /// 网关地址
    var gatewayUrl: String = "https://openapi.alipay.com/gateway.do"

    /// format
    val format: String = "json"

    ///
    val charset: String = "UTF-8"

    /// 签名算法
    val signType: String = "RSA2"

    /// 沙盒环境
//    val appId: String = "9021000144626469"
//    var gatewayUrl: String = "https://openapi-sandbox.dl.alipaydev.com/gateway.do"
//    val privateKey: String = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCG64In97jlgFB70OWvw9jrfO/tz8uLq4Ga8YxxLJwCNmZ9/ZsO0jEj/SvmWokhAx8+3fQ0kYstMX5pPvcUwYWZmQHYZHiRu9QVuQEtGsSXI3f+bOGGSb9ONnIUfGK5eG5/GbU/EIjIKFqVH1LNx98nCsJTwiR5q+YP/ycpNRDt6pPqKUpb5ApBL+Ww4Aub43FXLOUMREKwDw4G3JoTvrL+mS/+eRMGbznSzIBPcm8VskfDxZ5YmOPX5V8+rvWe/F9ypI1x4mlgKJgUMGgh9ZiFupQtKeKrG3QQi4e42IoyP5ELDhbI1CYY/jwiuFncwGSIJ+tZQl9bl3+vJ+Eegt3LAgMBAAECggEAZIYB8r9qXmxNCwNorp1v+Hyc0wX4dPby2LvR1NzU2/0mPgb/SXqoLFVMKRQZiaQm2pNKiQRLuv9eXRIRy8AoR3mt20l4X7SfXxP5YV7JTIabt8/t9DzsMVz1gHfwopmtUjuYRAWQs/kFDIhLDJE3nYIxqXBD9vdxsUd9KQGlvGztm9F+qjGglmK/mN1PhfuPLvZiZWA2ws4atqTQZNPzWVCaLQtgSAKvSSkiCx0SgZ3gM0bH6BXlP3zRZitHRQLADMcceeAJFIxbnuxm5ace6ORUedqvDmfrdOPOCHaPXaWzulHbcRexB/2ALwnqqwqYdnyToq2RvogUmYjl3oArIQKBgQDKPrZxOh17NpeyhpO8CyyVCm9ji5V2E12DQn4+NdbQeKpNFq/+1kSFLGWwzlCJWxyFHyfjdFmXk+M8COXuMXGU0GSQlmaZP7VzPs+pjYVIxqZOXlHRhLNGuSxN5huNniuyIAPsNyTuk+PJ6UtJ6MLSbSwCjonEzaxiZPjXdT68EQKBgQCqx9JxTjDji2ccm4hCkR9wAd10GnYE6LWaPWaZrl92Hd/lB9TrrNR4hByQ/6aVc/PCWpfk1E9q8G7IzbZgzNcVkO/7QiUrwymfl4Sl4tLSDPBEv3tX8rlDRIMmXE5KlbKBSX70sckKzPagZmM7zyiXI1c/nx77jk1Tq4NxyTyIGwKBgQCDHp93zx63+E/nTXrQakowIoDO//sZfzqcU+q2+H7Zz7FRo0jdJhtsX7laJJhwROK7p9Rbkd5X+GhVTsuLXwDtYyIf33KLHpPMMMe/fGISKcA+hMX02VzFmF4R7wKkEQzxQfJpHeNSzgzbpeXK+B3/AQqMac8GCfYeIKp4Pao7kQKBgCmU77tg02yD475GbF3w4duuyJHdY4D8fEMq0hF3MI0c5wfn6ULc2PrjB+WSKahYDDD1R+iSfSfHlOHVnKNjHLeGi7f8ufo20aqnBMgzOyujtNi4WrrmQY+MXfZ4ISPt4QGzuHKx2waDM0WIyH6miPaLx1GZV7zeQBuwNhf9bHyjAoGAOR+SM6bIN2i+/FX+mUSH2GiQFiiFD15BnOtL6pe6oUBbLuAofwrnM0/sNu1N1pk94IJqtNhkiHHHG97eH2r+QOAx+WA6SXKzEn0cBL0h4Yq4uCtvB1sIQU9PuazInIaL5/HlFuYNW8cD5GLerMnPQj0C3roJD5i5vN0zREVcHqU="
//    val publicKey: String = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAhuuCJ/e45YBQe9Dlr8PY63zv7c/Li6uBmvGMcSycAjZmff2bDtIxI/0r5lqJIQMfPt30NJGLLTF+aT73FMGFmZkB2GR4kbvUFbkBLRrElyN3/mzhhkm/TjZyFHxiuXhufxm1PxCIyChalR9SzcffJwrCU8IkeavmD/8nKTUQ7eqT6ilKW+QKQS/lsOALm+NxVyzlDERCsA8OBtyaE76y/pkv/nkTBm850syAT3JvFbJHw8WeWJjj1+VfPq71nvxfcqSNceJpYCiYFDBoIfWYhbqULSniqxt0EIuHuNiKMj+RCw4WyNQmGP48IrhZ3MBkiCfrWUJfW5d/ryfhHoLdywIDAQAB"

    @Autowired
    lateinit var orderMapper: PayOrderMapper

    @Autowired
    lateinit var memberShipAPI: MemberShipApi

    @PostMapping("pay/adultNotice", consumes = ["application/x-www-form-urlencoded;charset=UTF-8"])
    fun receiveAlipayResult(@RequestParam params: Map<String, Any>): XFResponseEntity {
        return appNotice(params)
    }

    @PostMapping("/pay/appNotice")
    fun appNotice(@RequestBody params: Map<String, Any>): XFResponseEntity {
        val orderNo: String? = params["out_trade_no"] as? String // 商户订单号
        val buyerPayNo: String? = params["buyer_logon_id"] as? String  // 买家支付宝账号
        val payStates: String? = params["trade_status"] as? String // 交易状态
        val buyerPayAmount: Double? = params["buyer_pay_amount"] as? Double // 用户支付金额
        val tradeNo: String? = params["trade_no"] as? String // 支付宝交易流水号
        val payMethod: Int? = params["payMethod"] as? Int
        val resultEntity = XFResponseEntity()
        resultEntity.code = 0
        resultEntity.msg = "success"
        if (orderNo != null) {
            try {
                val orderEntity = orderMapper.getPayOrderByOrderNo(orderNo)
                if (orderEntity != null) {
                    orderEntity.payState = handleState(payStates)
                    orderEntity.buyerPayNo = buyerPayNo
                    orderEntity.buyerPayAmount = buyerPayAmount?.toFloat()
                    orderEntity.tradeNo = tradeNo
                    if ((payMethod?:0) > 0) {
                        orderEntity.payMethod = payMethod?:0
                    }
                    orderMapper.updatePayOrder(orderEntity)
                    if (orderEntity.payState == 2) { // 支付成功
                        memberShipAPI.buyMemberShipSuccess(orderEntity.userId, orderEntity.productId?.toInt() ?: 0)
                    }

                } else {
                    /// 订单不存在
                    resultEntity.code = XFErrorCode.ORDER_NOT_FOUND.code
                    resultEntity.msg = XFErrorCode.ORDER_NOT_FOUND.message
                }
            } catch (error: Exception) {
                resultEntity.code = XFErrorCode.INTERNAL_ERROR.code
                resultEntity.msg = XFErrorCode.INTERNAL_ERROR.message
            }

        } else {
            /// 缺少订单号
            resultEntity.code = XFErrorCode.ORDER_NOT_FOUND.code
            resultEntity.msg = "缺少订单号"
        }
        return resultEntity
    }

    @PostMapping("/pay/orderState")
    fun getOrderState(@RequestBody param: Map<String, Any>): XFResponseEntity {
        val resultEntity = XFResponseEntity()
        try {
            val orderId = param["orderNo"] as? String
            if (orderId != null) {
                val orderEntity = orderMapper.getPayOrderByOrderNo(orderId)
                if (orderEntity == null) {
                    resultEntity.setError(XFErrorCode.ORDER_NOT_FOUND)
                } else {
                    resultEntity.result = orderEntity
                }
            } else {
                resultEntity.setError(XFErrorCode.PARAM_INVALID)
            }
            return resultEntity
        } catch (e: Error) {
            resultEntity.setError(XFErrorCode.SERVER_ERROR)
        }
        return resultEntity
    }

    private fun handleState(tradeStatus: String?): Int {
        when (tradeStatus) {
            "WAIT_BUYER_PAY" -> { // 交易创建，等待买家付款。
                return 5
            }

            "TRADE_CLOSED" -> { // 未付款交易超时关闭，或支付完成后全额退款。
                return 4
            }

            "TRADE_SUCCESS" -> { // 交易支付成功。
                return 2
            }

            "TRADE_FINISHED" -> { // 交易结束，不可退款。
                return 2
            }
        }
        return 0
    }

    @PostMapping("/pay/createOrder")
    fun createOrder(@RequestBody param: Map<String, Any>, @RequestHeader("token") token: String): XFResponseEntity {
        val responseEntity = XFResponseEntity()
        try {
            val userId = JWTManager.instance.verifyToken(token).toInt()
            if (userId > 0) {
                val orderEntity = PayOrderEntity.fromJson(param)
                orderEntity.userId = userId
                orderEntity.orderNo = generateOrderNumber()
                orderMapper.insertPayOrder(orderEntity)
                responseEntity.result = orderEntity
            } else {
                responseEntity.setError(XFErrorCode.INVALID_TOKEN)
            }

        } catch (e: Error) {
            responseEntity.setError(XFErrorCode.SERVER_ERROR)
        }

        return responseEntity
    }

    @PostMapping("/pay/payOrder")
    fun payOrder(@RequestBody param: Map<String, Any>, request: HttpServletRequest): XFResponseEntity {
        val userAgent = request.getHeader("User-Agent") ?: ""
        val isMobile = isMobileDevice(userAgent)
        val responseEntity = XFResponseEntity()
        val orderId = param["orderNo"] as? String
        if (orderId?.isEmpty() == true) {
            val error = XFResponseEntity()
            error.code = XFErrorCode.PARAM_INVALID.code
            error.msg = XFErrorCode.PARAM_INVALID.message
            return error
        }
        if (checkParams(param)) {
            val orderId = param["orderNo"] as String
            val orderEntity = orderMapper.getPayOrderByOrderNo(orderId)
            if (orderEntity == null) {
                val error = XFResponseEntity()
                error.code = XFErrorCode.ORDER_NOT_FOUND.code
                error.msg = XFErrorCode.ORDER_NOT_FOUND.message
                return error
            }
            /// 支付方式
            val payMethod = param["payMethod"] as Int
            orderEntity.payMethod = payMethod
            if (payMethod == 1) { // 支付宝
                val payResult = payAliOrder(orderEntity, isMobile)
                if (payResult != null) {
                    responseEntity.code = 0
                    responseEntity.msg = "success"
                    responseEntity.result = payResult
                }
            } else if (payMethod == 2) { // 微信

            }

        } else {
            /// 参数非法
            responseEntity.code = XFErrorCode.PARAM_INVALID.code
            responseEntity.msg = XFErrorCode.PARAM_INVALID.message
        }
        return responseEntity
    }

    /// 支付宝支付
    private fun payAliOrder(orderEntity: PayOrderEntity?, isMobile: Boolean): MutableMap<String, Any>? {
        // 创建AlipayClient实例
        val alipayClient: AlipayClient =
            DefaultAlipayClient(gatewayUrl, appId, privateKey, format, charset, publicKey, signType)
        val request = AlipayTradeAppPayRequest()
        //异步接收地址，仅支持http/https，公网可访问
        request.notifyUrl = "https://adult.xftiger.com/api/pay/adultNotice"
        //同步跳转地址，仅支持http/https
        request.returnUrl = ""
        /// 支付信息
        request.bizModel = createAliPayInfo(orderEntity, isMobile)
        /// 生成支付链接
        try {
            val resultStr = alipayClient.sdkExecute(request).body
            val resultMap: MutableMap<String, Any> = mutableMapOf()
            resultMap["payOrderInfo"] = resultStr
            return resultMap
        } catch (error: Exception) {
//            println(error.message)
        }
        return null
    }

    /// 微信支付
    /** 商户号  */
    var merchantId: String = "190000****"

    /** 商户API私钥路径  */
    val privateKeyPath: String = "/Users/<USER>/your/path/apiclient_key.pem"

    /** 商户证书序列号  */
    val merchantSerialNumber: String = "5157F09EFDC096DE15EBE81A47057A72********"

    /** 商户APIV3密钥  */
    val apiV3Key: String = "..."
    private fun createWeiChatPayInfo(orderEntity: PayOrderEntity): MutableMap<String, Any>? {

        // 使用自动更新平台证书的RSA配置
        // 一个商户号只能初始化一个配置，否则会因为重复的下载任务报错
        try {
            val config: Config =
                RSAAutoCertificateConfig.Builder()
                    .merchantId(merchantId)
                    .privateKeyFromPath(privateKeyPath)
                    .merchantSerialNumber(merchantSerialNumber)
                    .apiV3Key(apiV3Key)
                    .build()

            // 构建service
            val service = H5Service.Builder().config(config).build()

            // request.setXxx(val)设置所需参数，具体参数可见Request定义
            val request = PrepayRequest()
            val amount = Amount()
            amount.total = 100
            request.amount = amount
            request.appid = "wxa9d9651ae******"
            request.mchid = "190000****"
            request.description = "测试商品标题"
            request.notifyUrl = "https://notify_url"
            request.outTradeNo = "out_trade_no_001"
            // 调用下单方法，得到应答
            val response: PrepayResponse = service.prepay(request)
            val resultMap: MutableMap<String, Any> = mutableMapOf()
            resultMap["payUrl"] = response.h5Url
            return resultMap
        } catch (error: Exception) {
            println(error.message)
        }
        return null
    }

    /// 生成支付信息
    private fun createAliPayInfo(orderEntity: PayOrderEntity?, isMobile: Boolean): AlipayTradeAppPayModel {

        val model = AlipayTradeAppPayModel()

        // 设置商户订单号
        model.outTradeNo = orderEntity?.orderNo

        // 设置订单总金额
        model.totalAmount = orderEntity?.productPrice.toString()

        // 设置订单标题
        model.subject = orderEntity?.productTitle

        val nowTime = LocalDateTime.now().plusMinutes(30)
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
        model.timeExpire = nowTime.format(formatter)

        // 设置订单包含的商品列表信息
        val goodsDetail: MutableList<GoodsDetail> = ArrayList<GoodsDetail>()
        val goodsDetail0: GoodsDetail = GoodsDetail()
        goodsDetail0.goodsName = orderEntity?.productTitle
        goodsDetail0.quantity = 1L
        goodsDetail0.price = orderEntity?.productPrice.toString()
        goodsDetail0.goodsId = orderEntity?.productId
        goodsDetail.add(goodsDetail0)
        model.goodsDetail = goodsDetail

        return model
        /******必传参数******/
//        val bizContent = JSONObject()
//        //商户订单号，商家自定义，保持唯一性
//        bizContent["out_trade_no"] = orderEntity?.orderNo
//        //支付金额，最小值0.01元
//        bizContent["total_amount"] = orderEntity?.productPrice
//        //订单标题，不可使用特殊符号
//        bizContent["subject"] = orderEntity?.productTitle
//        if (isMobile) {
//            bizContent["product_code"] = "QUICK_MSECURITY_PAY"
//        } else {
//            bizContent["product_code"] = "QUICK_MSECURITY_PAY"
//        }
//        val nowTime = LocalDateTime.now().plusMinutes(30)
//        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
//        bizContent["time_expire"] = nowTime.format(formatter)
//         商品明细信息，按需传入
//        val goodsDetail = JSONArray()
//        val goods1 = JSONObject()
//        goods1["goods_name"] = orderEntity?.productTitle
//        goods1["quantity"] = 1
//        goods1["price"] = orderEntity?.productPrice
//        goodsDetail.add(goods1)
//        bizContent["goods_detail"] = goodsDetail
//        return bizContent.toString()
    }

    /// 支付参数校验
    fun checkParams(params: Map<String, Any>): Boolean {
        val payMethod = params["payMethod"] as? Int
        val orderId = params["orderNo"] as? String
        if (payMethod == null) {
            return false
        }
        if (orderId?.isEmpty() == true) {
            return false
        }
        return true
    }

    private fun isMobileDevice(userAgent: String): Boolean {
        // 简单判断是否为手机设备
        return userAgent.contains("Mobile", ignoreCase = true) ||
                userAgent.contains("Android", ignoreCase = true) ||
                userAgent.contains("iPhone", ignoreCase = true)
    }

    private val random = java.util.Random()

    /**
     * 生成支付订单号
     */
    fun generateOrderNumber(): String {
        val now = LocalDateTime.now()
        val formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss")
        val timestamp = now.format(formatter)
        val randomPart = String.format("%06d", random.nextInt(1000000))
        return "$timestamp$randomPart"
    }

}