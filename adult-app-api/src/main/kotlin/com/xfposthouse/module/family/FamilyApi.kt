package com.xfposthouse.module.family

import com.xfposthouse.jwt.JWTManager
import com.xfposthouse.mapper.FamilyMapper
import com.xfposthouse.mapper.UserMapper
import com.xfposthouse.network.entity.XFErrorCode
import com.xfposthouse.network.entity.XFResponseEntity
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestHeader
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/family")
class FamilyApi {

    @Autowired
    lateinit var familyMapper: FamilyMapper

    @Autowired
    lateinit var userMapper: UserMapper

    @PostMapping("info")
    fun getFamilyInfo(@RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            val userId = JWTManager.instance.verifyToken(token).toInt()
            if (userId > 0) {
                val familyId = userMapper.getFamilyId(userId) ?:0
                if (familyId > 0) {
                    val familyEntity = familyMapper.findFamily(familyId)
                    if (familyEntity != null) {
                        val list = familyMapper.findMembersById(familyId)
                        if (list.isNotEmpty()) {
                            familyEntity.members = userMapper.getUserListByIds(list)
                        }
                        result.result = familyEntity
                    } else {
                        result.setError(XFErrorCode.USER_FAMILY_NOT_EXIST)
                    }

                } else {
                    result.setError(XFErrorCode.USER_NO_FAMILY)
                }
            } else {
                result.setError(XFErrorCode.INVALID_TOKEN)
            }

        } catch (e: Error) {
            result.setError(XFErrorCode.SERVER_ERROR)
        }
        return result
    }

    /// 创建我的家庭
    @PostMapping("create")
    fun createFamily(@RequestBody param: Map<String, Any>, @RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            val name = param["name"] as String?
            if (name.isNullOrEmpty()) {
                result.setError(XFErrorCode.PARAM_INVALID)
                return result
            }
            val userId = JWTManager.instance.verifyToken(token).toInt()
            if (userId > 0) {
                val inviteCode = createInviteCode()
                val familyEntity = FamilyEntity()
                familyEntity.name = name
                familyEntity.userId = userId
                familyEntity.inviteCode = inviteCode
                familyMapper.insertFamily(familyEntity)
                familyEntity.id?.let {
                    userMapper.updateFamilyId(it, userId)
                    familyMapper.addMember(it, userId)
                }
                result.result = familyEntity

            } else {
                result.setError(XFErrorCode.INVALID_TOKEN)
            }

        } catch (e: Error) {
            result.setError(XFErrorCode.SERVER_ERROR)
        }
        return result
    }

    /// 加入家庭
    @PostMapping("add")
    fun joinFamily(@RequestBody param: Map<String, Any>, @RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            val userId = JWTManager.instance.verifyToken(token).toInt()
            if (userId > 0) {
                val myFamilyId = userMapper.getFamilyId(userId)
                if ((myFamilyId?:0) > 0) {
                    result.setError(XFErrorCode.USER_FAMILY_EXIST)
                    return result
                }
                val familyId = (param["familyId"]).toString().toInt()
                val inviteCode = param["inviteCode"].toString()
                val familyEntity = familyMapper.findFamily(familyId)
                if (inviteCode == familyEntity?.inviteCode) {
                    familyMapper.addMember(familyId, userId)
                    userMapper.updateFamilyId(familyId, userId)
                } else {
                    result.setError(XFErrorCode.USER_INVITE_CODE_ERROR)
                }
            } else {
                result.setError(XFErrorCode.INVALID_TOKEN)
            }

        } catch (e: Error) {
            result.setError(XFErrorCode.SERVER_ERROR)
        }
        return result
    }

    /// 生成邀请码
    private fun createInviteCode(): String {
        val code = (Math.random() * 900000).toInt() + 100000
        return code.toString()
    }
}