CREATE TABLE wechat_user
(
    id          BIGINT     NOT NULL AUTO_INCREMENT COMMENT '自增id',
    user_id     BIGINT     NOT NULL DEFAULT 0 COMMENT '用户id',
    openid      VARCHAR(255)        DEFAULT '' COMMENT '微信openid',
    unionid     VARCHAR(255)        DEFAULT '' COMMENT '微信unionid',
    deleted     TINYINT(1) NOT NULL DEFAULT 0 COMMENT '删除标识：0-正常，1-已删除',
    create_time DATETIME   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT ='微信用户表';

CREATE TABLE user_oauth_bindings (
    id              BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id         BIGINT     NOT NULL DEFAULT 0 COMMENT '用户id',
    type            INT NOT NULL, -- 第三方平台：wechat, alipay, github, google, apple
    openid          VARCHAR(255) NOT NULL, -- 平台唯一用户标识
    unionid         VARCHAR(255), -- 微信、支付宝有时返回
    access_token    TEXT,
    refresh_token   TEXT,
    expires_at      DATETIME,
    bind_status     TINYINT DEFAULT 1, -- 1=已绑定, 0=解绑
    created_at      DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at      DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
);