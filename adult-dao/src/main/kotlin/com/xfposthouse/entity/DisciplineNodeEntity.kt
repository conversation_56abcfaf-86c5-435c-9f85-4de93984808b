package com.xfposthouse.entity

import com.mybatisflex.annotation.*
import java.time.LocalDateTime
import java.time.LocalTime

@Table("plan_nodes")
class DisciplineNodeEntity(
    @Id(keyType = KeyType.Auto)// AUTO_INCREMENT
    var id: Long? = null,

    @Column("plan_id")
    var planId: Long,

    @Column("user_id")
    var userId: Long,

    var name: String,

    var description: String? = null,

    var weekday: Int? = null, // tinyint

    var time: LocalTime? = null,

    @Column("created_at")
    var createdAt: LocalDateTime? = null,
) {

}