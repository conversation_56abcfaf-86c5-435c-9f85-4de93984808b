package com.xfposthouse.module.life_record.feedback

import com.xfposthouse.module.life_record.entity.LifeRecordEntity
import com.xfposthouse.module.life_record.entity.LifeReportEntity
import com.xfposthouse.module.life_record.feedback.mapper.ReportMapper
import com.xfposthouse.network.entity.XFErrorCode
import com.xfposthouse.network.entity.XFResponseEntity
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/report")
class ReportApi {
    @Autowired
    lateinit var reportMapper: ReportMapper

    @PostMapping("/create")
    fun createFeedBack(@RequestBody param: Map<String, Any>): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            // 插入 LifeRecord 数据
            val lifeJson = mutableMapOf<String, Any>()
            lifeJson.putAll(param)
            lifeJson["createTime"] = System.currentTimeMillis()
            reportMapper.insertReport(LifeReportEntity.fromJson(lifeJson))
            return result
        } catch (e: Error) {
            // 发生错误时，返回 500 Internal Server Error
            result.code = XFErrorCode.SERVER_ERROR.code
            result.msg = XFErrorCode.SERVER_ERROR.message
            println(e.toString())
            return result
        }
    }

    @PostMapping("/list")
    fun listLifeRecord(@RequestBody param: Map<String, Any>): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            // 插入 LifeRecord 数据
            var lifeRecordList: List<LifeReportEntity>
            if (param.containsKey("userId")) {
                val userId = param["userId"] as Int
                lifeRecordList = reportMapper.selectReportByAuthorId(userId)
            } else {
                lifeRecordList = reportMapper.getReports()
            }
            val resultJson = mutableMapOf<String, Any>()
            resultJson["list"] = lifeRecordList
            result.result = resultJson
            return result
        } catch (e: Error) {
            // 发生错误时，返回 500 Internal Server Error
            result.code = XFErrorCode.SERVER_ERROR.code
            result.msg = XFErrorCode.SERVER_ERROR.message
            println(e.toString())
            return result
        }
    }
}