package com.xfposthouse.module.life_record.entity

data class LifeLikeEntity(
    val id: Long = 0L,

    val postId: Long,

    val userId: Long,

    val createdAt: Long? = null,
) {
    companion object {
        fun fromJson(json: Map<String, Any>): LifeLikeEntity {
            return LifeLikeEntity(
                postId = json.parseLong("postId", required = true),
                userId = json.parseLong("userId", required = true),
                createdAt = json.parseNullableLong("createdAt"),
            )
        }

        // 扩展函数：解析Long类型（强制要求字段存在）
        private fun Map<String, Any>.parseLong(key: String, required: Boolean): Long {
            return this[key]?.toString()?.toLongOrNull()
                ?: if (required) throw IllegalArgumentException("Missing required field: $key") else 0L
        }

        // 扩展函数：解析可空Long类型
        private fun Map<String, Any>.parseNullableLong(key: String): Long? {
            return this[key]?.toString()?.toLongOrNull()
        }

        // 扩展函数：解析String类型（强制要求字段存在）
        private fun Map<String, Any>.parseString(key: String): String {
            return this[key] as? String
                ?: throw IllegalArgumentException("Field $key is not a String or is missing")
        }

        // 扩展函数：解析Int类型（允许空值，返回null）
        private fun Map<String, Any>.parseInt(key: String): Int? {
            return when (val value = this[key]) {
                is Number -> value.toInt()
                is String -> value.toIntOrNull()
                else -> null
            }
        }
    }

}