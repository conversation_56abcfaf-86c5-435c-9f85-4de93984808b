package com.xfposthouse.network.entity

import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName

enum class XFErrorCode(val code: Int, val message: String) {
    SERVER_ERROR(1001, "Server Error"),
    NOT_FOUND(404, "Resource not found"),
    INTERNAL_ERROR(500, "Internal server error"),
    UNAUTHORIZED(401, "Unauthorized access"),
    FORBIDDEN(403, "Forbidden access"),
    METHOD_NOT_ALLOWED(405, "Method not allowed"),
    PARAM_INVALID(1002, "参数非法"),
    INVALID_TOKEN(1003, "Invalid token"),
    EXIST_ACCOUNT(1004, "账号已存在"),
    /// 订单相关错误码
    ORDER_NOT_FOUND(11001, "订单不存在"),
    ORDER_NO_RESULT(11002, "暂无结果"),
    UNKNOWN(1000, "未知错误"),
    /// 用户
    USER_CODE_ERROR(12000, "验证码错误"),
    USER_NO_PREMISS(12001, "没有权限"),
    USER_NO_SPACE(12002, "云存储空间不足"),
    USER_UN_REGISTER(12003, "该用户已被注销"),
    USER_BLACK_SELF(12004, "不能屏蔽自己"),
    USER_NO_FAMILY(12005, "该家庭不存在"),
    USER_INVITE_CODE_ERROR(12006, "邀请不存在或已过期"),
    USER_FAMILY_NOT_EXIST(12007, "家庭不存在或被删除"),
    USER_FAMILY_EXIST(12008, "已有家庭，不能再加入其他的家庭"),
    USER_NO_PERMISSION(12009, "没有权限"),
    USER_AUTH_ERROR(12010, "授权失败"),
    USER_LOGIN_TYPE_ERROR(12011, "不支持的登录方式"),
    /** 账簿 */
    LEDGER_BOOK_EXIST(13000, "该账簿已经存在")
    ;

    override fun toString(): String {
        return "ErrorCode{code=$code, message='$message'}"
    }
}

class XFResponseEntity {
    var code:Int = 0;
    var msg:String = "success"
    var result:Any? = null
    var data:Any? = null

    fun setError(error: XFErrorCode) {
        code = error.code
        msg = error.message
    }
}