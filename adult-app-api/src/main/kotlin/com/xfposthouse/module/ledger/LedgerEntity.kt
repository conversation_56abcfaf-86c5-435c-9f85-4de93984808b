package com.xfposthouse.module.ledger

import com.mybatisflex.annotation.Id
import com.mybatisflex.annotation.KeyType
import com.mybatisflex.annotation.Table
import java.math.BigDecimal
import java.time.LocalDateTime

@Table(value = "ledger")
data class LedgerEntity(
    @Id(keyType = KeyType.Auto)
    val id: Long? = null,

    /** 账本id */
    var bookId: Long = 0,

    var userId: Long = 0,

    /** 类型：0=支出，1=收入 */
    val type: Boolean,

    /** 分类，如餐饮、交通、工资 */
    val category: String? = null,

    /** 子分类，如早餐、公交、年终奖 */
    val subCategory: String? = null,

    /** 金额 */
    val amount: BigDecimal,

    /** 支付账户 */
    val accountFrom: String? = null,

    /** 收款账户 */
    val accountTo: String? = null,

    /** 账目日期 */
    var recordDate: LocalDateTime = LocalDateTime.now(),

    /** 创建时间 */
    val createTime: LocalDateTime = LocalDateTime.now(),

    /** 备注 */
    var description: String? = null,

    /** 是否删除（0正常，1删除） */
    val deleted: Int = 0,

    /** 是否语音记账 */
    var isVoice: Boolean = false
) {}