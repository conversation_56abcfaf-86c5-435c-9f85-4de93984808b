package com.xfposthouse.user.api

import com.xfposthouse.jwt.JWTManager
import com.xfposthouse.mapper.UserBlackMapper
import com.xfposthouse.network.entity.XFErrorCode
import com.xfposthouse.network.entity.XFResponseEntity
import com.xfposthouse.user.entity.XFUserEntity
import com.xfposthouse.user.entity.XFUserTokenEntity
import com.xfposthouse.mapper.UserMapper
import com.xfposthouse.module.ledger.LedgerAPI
import com.xfposthouse.module.ledger.LedgerAddBookParam
import com.xfposthouse.module.ledger.LedgerBookEntity
import com.xfposthouse.user.entity.XFAdminBlockEntity
import com.xfposthouse.util.CommonUtil
import com.google.gson.Gson
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*

const val Default_User_Space: Long = 50 * 1024 * 1024

const val Default_User_Voice_Count: Int = 10

@RestController
@RequestMapping("/api")
class XFUserAPI {
    @Autowired
    lateinit var userMapper: UserMapper

    @Autowired
    lateinit var userBlackMapper: UserBlackMapper

    @Autowired
    lateinit var smsCodeServer: SmsCodeServer

    @Autowired
    lateinit var ledgerAPI: LedgerAPI

    @PostMapping("/user/login")
    @CrossOrigin
    fun login(@RequestBody param: Map<String, Any>): XFResponseEntity {
        val userName: String = param["userName"] as String
        val password: String? = param["password"] as? String
        val code: String? = param["code"] as? String
        var checkCode = false
        if (code?.isNotEmpty() == true) {
            checkCode = smsCodeServer.checkCode(code, userName)
        }
        val result = XFResponseEntity()
        val user: XFUserEntity? = userMapper.getUserByUserName(userName)
        val localPdw = userMapper.getUserPassword(userName)?:"-1"
        if (user != null) {
            if (checkCode || localPdw == password) {
                if (user.userState == 3) {
                    result.setError(XFErrorCode.USER_UN_REGISTER)
                    return result
                }
                val tokenEntity = XFUserTokenEntity()
                val token = JWTManager.instance.createJWT(user.userId.toString(), localPdw)
                tokenEntity.userInfo = user
                tokenEntity.token = token
                result.result = tokenEntity
            } else {
                result.code = 1001
                if (code.isNullOrEmpty()) {
                    result.msg = "密码错误"
                } else {
                    result.msg = "验证码错误"
                }
            }
        } else {
            result.code = 1002
            result.msg = "该账号尚未注册"
        }
        return result
    }

    @PostMapping("/user/register")
    fun register(@RequestBody param: Map<String, Any>): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            val userName: String = param["userName"] as String
            val password: String = param["password"] as String
            val code: String = param["code"] as String
            val checkCode: Boolean = smsCodeServer.checkCode(code, userName)
            if (!checkCode) {
                result.setError(XFErrorCode.USER_CODE_ERROR)
                return result
            }
            var user: XFUserEntity? = userMapper.getUserByUserName(userName)
            if (user == null) {
                user = XFUserEntity()
                user.userName = userName
                user.phoneNumber = userName
                user.totalSpaceSize = Default_User_Space
                user.totalVoiceCount = Default_User_Voice_Count
                user.nickName = "用户-" + userName.takeLast(4)
                user.avatar = "https://file-xfpost-1317756102.cos.ap-shanghai.myqcloud.com/user/defaultAvatar.png"
                user.registerTime = System.currentTimeMillis()
                userMapper.insertUser(user, password)
                val tokenEntity = XFUserTokenEntity()
                val token = JWTManager.instance.createJWT(user.userId.toString(), password)
                tokenEntity.userInfo = user
                tokenEntity.token = token
                result.result = tokenEntity
                createDefaultLedgerBook(user.userId)
            } else {
                result.setError(XFErrorCode.EXIST_ACCOUNT);
            }
        } catch (e: Error) {
            result.setError(XFErrorCode.SERVER_ERROR)
        }
        return result
    }

    @PostMapping("/user/edit")
    fun editUser(@RequestBody param: Map<String, Any>, @RequestHeader("token") token: String): XFResponseEntity {

        try {
            val userId = JWTManager.instance.verifyToken(token)
            val user: XFUserEntity? = userMapper.getUserByUserId(userId.toInt())
            val userMap = CommonUtil.entityToMap(user!!)
            param.forEach { (key, value) ->
                userMap[key] = value
            }
            val userJson: String = Gson().toJson(userMap)
            val userInfo: XFUserEntity = Gson().fromJson(userJson, XFUserEntity().javaClass)
            userMapper.updateUser(userInfo)
            val result = XFResponseEntity()
            result.code = 0
            result.msg = "success"
            result.result = userInfo
            return result
        } catch (error: Error) {
            val result = XFResponseEntity()
            result.code = -1000
            result.msg = error.toString()
            return result
        }
    }

    @PostMapping("/user/resetPwd")
    fun getUserInfo(@RequestBody param: Map<String, Any>): XFResponseEntity {
        val result = XFResponseEntity()
        return try {
            val userName: String = param["userName"] as String
            val password: String = param["password"] as String
            val code: String = param["code"] as String
            val checkCode: Boolean = smsCodeServer.checkCode(code, userName)
            if (!checkCode) {
                result.code = XFErrorCode.USER_CODE_ERROR.code
                result.msg = XFErrorCode.USER_CODE_ERROR.message
                return result
            }
            var user: XFUserEntity? = userMapper.getUserByUserName(userName)
            if (user != null) {
                userMapper.updatePassWord(user.userId, password)
            }
            result
        } catch (error: Error) {
            result.setError(XFErrorCode.SERVER_ERROR)
            result
        }
    }

    @PostMapping("/user/getInfo")
    fun getMyUserInfo(@RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()
        return try {
            val userId = JWTManager.instance.verifyToken(token)
            if (userId.toInt() <= 0) {
                result.setError(XFErrorCode.INVALID_TOKEN)
                return result
            }
            val user = userMapper.getUserByUserId(userId.toInt())
            if (user != null) {
                checkMemberShip(user)
                result.result = user
            } else {
                result.setError(XFErrorCode.INVALID_TOKEN)
            }
            result
        } catch (error: Error) {
            result.setError(XFErrorCode.SERVER_ERROR)
            result
        }
    }

    /// 获取他人简洁用户信息
    @PostMapping("/user/shortInfo")
    fun getOtherUserInfo(@RequestBody param: Map<String, Any>, @RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()
        return try {
            val userId = param["userId"].toString().toInt()
            if (userId <= 0) {
                result.setError(XFErrorCode.INVALID_TOKEN)
                return result
            }
            val user = userMapper.getShortUserInfo(userId)
            if (user != null) {
                result.result = user
            } else {
                result.setError(XFErrorCode.PARAM_INVALID)
            }
            result
        } catch (error: Error) {
            result.setError(XFErrorCode.SERVER_ERROR)
            result
        }
    }

    @PostMapping("/user/addVoiceCount")
    fun addVoiceCount(@RequestBody param: Map<String, Any>, @RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()
        return try {
            val userId = JWTManager.instance.verifyToken(token).toInt()
            val user = userMapper.getUserByUserId(userId)
            if (user != null) {
                user.usedVoiceCount += 1
                userMapper.updateUsedVoiceCount(userId, user.usedVoiceCount)
            } else {
                result.setError(XFErrorCode.INVALID_TOKEN)
            }
            result
        } catch (error: Error) {
            result.setError(XFErrorCode.SERVER_ERROR)
            result
        }
        return result
    }

    @PostMapping("/user/delete")
    fun deleteUser(@RequestBody param: Map<String, Any>, @RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()
        return try {
            val userId = JWTManager.instance.verifyToken(token).toInt()
            val user = userMapper.getUserByUserId(userId)
            if (user != null) {
//                user.userState += 3
                userMapper.updateUserState(userId, 3)
            } else {
                result.setError(XFErrorCode.INVALID_TOKEN)
            }
            result
        } catch (error: Error) {
            result.setError(XFErrorCode.SERVER_ERROR)
            result
        }
        return result
    }

    /// 拉黑用户
    @PostMapping("/user/black")
    fun blackUser(@RequestBody param: Map<String, Any>, @RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()
        return try {
            val userId = JWTManager.instance.verifyToken(token).toInt()
            val user = userMapper.getUserByUserId(userId)
            if (user != null) {
                val blackUserId = param["userId"].toString().toInt()
                if (userId == blackUserId) {
                    result.setError(XFErrorCode.USER_BLACK_SELF)
                } else {
                    userBlackMapper.insertSafely(userId, blackUserId)
                }
            } else {
                result.setError(XFErrorCode.INVALID_TOKEN)
            }
            result
        } catch (error: Error) {
            result.setError(XFErrorCode.SERVER_ERROR)
            result
        }
        return result
    }

    /// 后台屏蔽用户
    @PostMapping("/admin/black")
    fun adminBlackUser(@RequestBody param: Map<String, Any>, @RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()
        return try {
            val userId = JWTManager.instance.verifyToken(token).toInt()
            val role: Int = userMapper.getUserRole(userId)
            if (role > 0) {
                val blackUserId = param["userId"].toString().toInt()
                if (userId == blackUserId) {
                    result.setError(XFErrorCode.USER_BLACK_SELF)
                } else {
                    val adminBlock = XFAdminBlockEntity(adminId = userId, targetUserId = blackUserId, blockType = 2)
                    userBlackMapper.insertBlockRecord(adminBlock)
                    userMapper.updateUserState(blackUserId, 0)
                }
            } else {
                result.setError(XFErrorCode.INVALID_TOKEN)
            }
            result
        } catch (error: Error) {
            result.setError(XFErrorCode.SERVER_ERROR)
            result
        }
        return result
    }

    /// 获取用户列表
    @PostMapping("/user/list")
    fun getUserList(@RequestBody param: Map<String, Any>, @RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()
        return try {
            val userId = JWTManager.instance.verifyToken(token).toInt()
            val pageSize = param["pageSize"] as? Int ?: 20
            val pageIndex = param["pageIndex"] as? Int ?: 1
            var userInfo = userMapper.getShortUserInfo(userId)
            var role: Int = 0
            if (userInfo != null) {
                role = (userInfo["role"]?:"0").toString().toInt()
            }
            if (role > 0) {
                val list = userMapper.getUserList(pageSize, (pageIndex - 1) * pageSize)
                val totalCount = userMapper.getUsersCount()
                result.result = mapOf("totalCount" to totalCount, "list" to list)
            } else {
                result.setError(XFErrorCode.USER_NO_PERMISSION)
            }
            result
        } catch (error: Error) {
            result.setError(XFErrorCode.SERVER_ERROR)
            result
        }
        return result
    }

    /// 更新已使用空间
    fun updateUsedSpace(userId: Int, size: Long) {
        userMapper.updateUsedSpace(userId, size)
    }

    /// 获取用户空间使用情况
    fun getUserSpaceUsedInfo(userId: Int): Pair<Long, Long> {
        val result = userMapper.getUserSpaceUsage(userId)
        val usedSpace = (result?.get("usedSpace") as Long?)?:0
        val totalSpace = (result?.get("totalSpace") as Long?)?:0
        return Pair(usedSpace, totalSpace)
    }

    /// 校验会员信息是否过期
    private fun checkMemberShip(user: XFUserEntity) {
        if (user.membershipId != null) {
            val endTime = user.membershipEndTime
            if (endTime != null && endTime <= System.currentTimeMillis()) {
                // 会员过期
                user.membershipId = null
                user.membershipLevel = 0
                user.totalSpaceSize = Default_User_Space
                user.totalVoiceCount = Default_User_Voice_Count
                userMapper.updateUser(user)
            }
        }
    }

    /**
     * 创建默认账本
     * */
    private fun createDefaultLedgerBook(userId: Int) {
        val book = LedgerAddBookParam()
        book.name = "家庭账簿"
        ledgerAPI.addBook(book, userId.toLong())
    }

    /// Todo - 定时全量更新会员过期信息
}