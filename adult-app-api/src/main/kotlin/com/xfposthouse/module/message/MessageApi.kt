package com.xfposthouse.module.message

import com.xfposthouse.jwt.JWTManager
import com.xfposthouse.mapper.MessageMapper
import com.xfposthouse.module.space.entity.SpaceEntity
import com.xfposthouse.network.entity.XFErrorCode
import com.xfposthouse.network.entity.XFResponseEntity
import com.xfposthouse.util.XFLog
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/message")
class MessageApi {

    @Autowired
    lateinit var messageMapper: MessageMapper

    @PostMapping("myList")
    fun getMessageList(@RequestBody param: Map<String, Any>, @RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            val userId = JWTManager.instance.verifyToken(token).toInt()
            if (userId > 0) {
                val pageSize = param["pageSize"] as? Int ?: 20
                val pageIndex = param["pageIndex"] as? Int ?: 1
                val resultMap = mutableMapOf<String, Any?>()
                resultMap["list"] = messageMapper.findMessagesByUserId(userId, pageSize,(pageIndex - 1) * pageSize)
                result.result = resultMap
            } else {
                result.setError(XFErrorCode.INVALID_TOKEN)
            }

        } catch (e: Error) {
            result.setError(XFErrorCode.SERVER_ERROR)
        }
        return result
    }

    @PostMapping("newCount")
    fun getMessageNewCount(@RequestBody param: Map<String, Any>, @RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            val userId = JWTManager.instance.verifyToken(token).toInt()
            if (userId > 0) {
                val count = messageMapper.findUnReadMessageCount(userId)
                result.result = mapOf("count" to count)
            } else {
                result.setError(XFErrorCode.INVALID_TOKEN)
            }
        } catch (e: Error) {
            result.setError(XFErrorCode.SERVER_ERROR)
        }
        return result
    }

    /// 读取消息
    fun readMessage(userId: Int, referId: Long) {
        try {
            messageMapper.updateIsRead(userId, referId)
        } catch (e: Error) {
            XFLog.debugLog(e.message.toString())
        }
    }

    /// 添加评论消息
    fun createCommentMessage(param: Map<String, Any>) {
        try {
            val fromUserId = param["userId"].toString().toInt()
            val toUserId = param["toUserId"].toString().toInt()
            if (fromUserId == toUserId) {
                return
            }
            val referId = param["postId"].toString().toLong()
            val fromUserName = param["nickName"].toString()
            val resultMap = HashMap<String, Any>()
            resultMap["receiverId"] = toUserId
            resultMap["content"] = "用户#${fromUserName}#评论了你发布的动态"
            resultMap["fromId"] = fromUserId
            resultMap["referId"] = referId
            resultMap["createdAt"] = System.currentTimeMillis()
            resultMap["messageType"] = 1
            messageMapper.insertMessage(MessageEntity.fromJson(resultMap))
        } catch (e: Error) {
            XFLog.debugLog(e.message.toString())
        }
    }

//    fun createCommentMessage(@RequestBody param: Map<String, Any>): XFResponseEntity {
//        val result = XFResponseEntity()
//        try {
//            val userId = param["userId"].toString().toInt()
//            val resultMap = HashMap<String, Any>()
//            resultMap["receiver_id"] = userId
//
//        } catch (e: Error) {
//            result.code = XFErrorCode.SERVER_ERROR.code
//            result.msg = XFErrorCode.SERVER_ERROR.message
//            return result
//        }
//        return result
//    }
}