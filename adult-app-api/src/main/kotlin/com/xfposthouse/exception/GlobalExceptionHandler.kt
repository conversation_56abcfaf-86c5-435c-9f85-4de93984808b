package com.xfposthouse.exception

import com.xfposthouse.network.entity.XFErrorCode
import com.xfposthouse.network.entity.XFResponseEntity
import io.github.oshai.kotlinlogging.KotlinLogging
import jakarta.servlet.http.HttpServletRequest
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.RestControllerAdvice
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler

@RestControllerAdvice
class GlobalExceptionHandler : ResponseEntityExceptionHandler() {

    private val logger = KotlinLogging.logger {}

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException::class)
    fun handleBusinessException(
        request: HttpServletRequest,
        ex: BusinessException
    ): ResponseEntity<XFResponseEntity> {
        logger.warn { "业务异常: ${ex.message}, URI: ${request.requestURI}" }
        
        val response = XFResponseEntity().apply {
            setError(ex.errorCode)
        }
        return ResponseEntity.ok(response)
    }

    /**
     * 处理JWT Token异常
     */
    @ExceptionHandler(TokenException::class)
    fun handleTokenException(
        request: HttpServletRequest,
        ex: TokenException
    ): ResponseEntity<XFResponseEntity> {
        logger.warn { "Token异常: ${ex.message}, URI: ${request.requestURI}" }
        
        val response = XFResponseEntity().apply {
            setError(XFErrorCode.INVALID_TOKEN)
        }
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response)
    }

    /**
     * 处理参数校验异常
     */
    @ExceptionHandler(IllegalArgumentException::class)
    fun handleIllegalArgumentException(
        request: HttpServletRequest,
        ex: IllegalArgumentException
    ): ResponseEntity<XFResponseEntity> {
        logger.warn { "参数异常: ${ex.message}, URI: ${request.requestURI}" }
        
        val response = XFResponseEntity().apply {
            setError(XFErrorCode.PARAM_INVALID)
        }
        return ResponseEntity.badRequest().body(response)
    }

    /**
     * 处理类型转换异常
     */
    @ExceptionHandler(ClassCastException::class, NumberFormatException::class)
    fun handleTypeException(
        request: HttpServletRequest,
        ex: Exception
    ): ResponseEntity<XFResponseEntity> {
        logger.warn { "类型转换异常: ${ex.message}, URI: ${request.requestURI}" }
        
        val response = XFResponseEntity().apply {
            setError(XFErrorCode.PARAM_INVALID)
        }
        return ResponseEntity.badRequest().body(response)
    }

    /**
     * 处理所有未捕获的异常
     */
    @ExceptionHandler(Exception::class)
    fun handleGenericException(
        request: HttpServletRequest,
        ex: Exception
    ): ResponseEntity<XFResponseEntity> {
        logger.error(ex) { "系统异常: ${ex.message}, URI: ${request.requestURI}" }
        
        val response = XFResponseEntity().apply {
            setError(XFErrorCode.SERVER_ERROR)
        }
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response)
    }
}