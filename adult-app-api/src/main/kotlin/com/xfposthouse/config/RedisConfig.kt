package com.xfposthouse.config

import org.springframework.context.annotation.Configuration

@Configuration
class RedisConfig {
//    @Bean
//    fun redisson(): RedissonClient {
//        val config = Config()
//        config.useSingleServer().address = "redis://127.0.0.1:6379"
//        config.useSingleServer().database = 0
////        config.useSingleServer().password = ""
//        config.codec = JsonJacksonCodec()
//        return Redisson.create(config)
//    }
}