package com.xfposthouse.module.life_record

import com.alibaba.fastjson.JSON
import com.xfposthouse.jwt.JWTManager
import com.xfposthouse.mapper.*
import com.xfposthouse.module.life_record.entity.LifeCommentsEntity
import com.xfposthouse.module.life_record.entity.LifeLikeEntity
import com.xfposthouse.module.life_record.entity.LifeRecordEntity
import com.xfposthouse.module.message.MessageApi
import com.xfposthouse.module.space.SpaceAPI
import com.xfposthouse.network.entity.XFErrorCode
import com.xfposthouse.network.entity.XFResponseEntity
import com.xfposthouse.security.XFSecurity
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/lifeRecord")
class LifeRecordApi {

    @Autowired
    private lateinit var userMapper: UserMapper

    @Autowired
    lateinit var lifeRecordMapper: LifeRecordMapper

    @Autowired
    lateinit var commentsMapper: LifeCommentsMapper

    @Autowired
    lateinit var likeMapper: LifeLikesMapper

    @Autowired
    lateinit var blackMapper: UserBlackMapper

    @Autowired
    lateinit var spaceAPI: SpaceAPI

    @Autowired
    lateinit var messageApi: MessageApi

    @PostMapping("/create")
    fun createLifeRecord(@RequestBody param: Map<String, Any>, @RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            val userId = JWTManager.instance.verifyToken(token).toInt()
            if (userId > 0) {
                // 插入 LifeRecord 数据
                if (checkLifeRecordParam(param)) {
                    val lifeJson = mutableMapOf<String, Any>()
                    lifeJson.putAll(param)
                    val nowTime = System.currentTimeMillis()
                    lifeJson["publishTime"] = nowTime
                    lifeJson["createTime"] = nowTime
                    lifeJson["authorId"] = userId
                    val totalFileSize = (param["totalFileSize"] as? Number)?.toLong() ?: 10995116277760
                    val userSpacePair = spaceAPI.getUserSpaceUsedInfo(userId)
                    // 用户总私有云空间大小
                    val totalSpaceSize = userSpacePair.second
                    // 已使用云空间大小
                    val usedSpaceSize = userSpacePair.first
                    if (totalFileSize + usedSpaceSize > totalSpaceSize) {
                        result.setError(XFErrorCode.USER_NO_SPACE)
                        return result
                    }
                    val lifeRecordEntity = LifeRecordEntity.fromJson(lifeJson)
                    lifeRecordEntity.title = lifeRecordEntity.title?.let { XFSecurity.replaceSensitive(it) }
                    lifeRecordEntity.description = lifeRecordEntity.description?.let { XFSecurity.replaceSensitive(it) }
                    lifeRecordMapper.insertLifeRecord(lifeRecordEntity, JSON.toJSONString(lifeRecordEntity.fileNames), JSON.toJSONString(lifeRecordEntity.imageUrls))
                    lifeJson["blogId"] = lifeRecordEntity.id?:0
                    val createResult = spaceAPI.createImageFile(lifeJson, token, userSpacePair)
                    if (createResult.code != 0) {
                        return createResult
                    }
                    result.result = lifeRecordEntity
                } else {
                    result.setError(XFErrorCode.PARAM_INVALID)
                }
            } else {
                result.setError(XFErrorCode.INVALID_TOKEN)
            }
            return result
        } catch (e: Error) {
            // 发生错误时，返回 500 Internal Server Error
            result.setError(XFErrorCode.SERVER_ERROR)
            return result
        }
    }

    /// 所有帖子列表，后台管理使用
    @PostMapping("/managerList")
    fun getAllLifeRecords(@RequestBody param: Map<String, Any>): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            val pageSize = param["pageSize"] as? Int ?: 20
            val pageIndex = param["pageIndex"] as? Int ?: 1
            val list = lifeRecordMapper.selectLifeRecords(pageSize, (pageIndex - 1) * pageSize)
            val totalCount = lifeRecordMapper.getRecordCount()
            val res = mutableMapOf<String, Any>()
            res["totalCount"] = totalCount
            res["list"] = list
            result.result = res
            return result
        } catch (e: Error) {
            // 发生错误时，返回 500 Internal Server Error
            result.setError(XFErrorCode.SERVER_ERROR)
            return result
        }
    }

    /// 广场帖子列表
    @PostMapping("/list")
    fun listLifeRecord(@RequestBody param: Map<String, Any>, @RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            val userId = JWTManager.instance.verifyToken(token).toInt()
            if (userId > 0) {
                val pageSize = param["pageSize"] as? Int ?: 20
                val pageIndex = param["pageIndex"] as? Int ?: 1
                /// 获取屏蔽用户ids
                val ids = blackMapper.listBlockedUsers(userId, 0, 30)
                val lifeRecordList: List<LifeRecordEntity> = if (ids.isEmpty()) {
                    lifeRecordMapper.getLifeRecords(pageSize, (pageIndex - 1) * pageSize)
                } else {
                    lifeRecordMapper.getLifeRecordsByUsers(ids, pageSize, (pageIndex - 1) * pageSize)
                }
                for (lifeRecord in lifeRecordList) {
                    lifeRecord.likedCount = likeMapper.countByPostId(lifeRecord.id)
                    lifeRecord.isLiked = likeMapper.existsByCompositeId(lifeRecord.id, userId)
                    lifeRecord.commentCount = commentsMapper.countByPostId(lifeRecord.id)
                }
                val resultJson = mutableMapOf<String, Any>()
                resultJson["list"] = lifeRecordList
                result.result = resultJson
            }  else {
                result.setError(XFErrorCode.INVALID_TOKEN)
            }

            return result
        } catch (e: Error) {
            // 发生错误时，返回 500 Internal Server Error
            result.setError(XFErrorCode.SERVER_ERROR)
            return result
        }
    }

    /// 我的帖子列表
    @PostMapping("/myList")
    fun myLifeRecords(@RequestBody param: Map<String, Any>, @RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            val userId = JWTManager.instance.verifyToken(token).toInt()
            val lifeRecordList: List<LifeRecordEntity>
            if (userId > 0) {
                var createTime = param["createTime"] as? Long
                val pageSize = param["pageSize"] as? Int ?: 10
                val isLast = param["isLast"] as? Boolean ?: false
                createTime = createTime ?: System.currentTimeMillis()
                lifeRecordList = if (isLast) {
                    lifeRecordMapper.getLastRecords(userId, createTime, pageSize)
                } else {
                    lifeRecordMapper.getFrontRecords(userId, createTime, pageSize)
                }
                for (item in lifeRecordList) {
                    item.likedCount = likeMapper.countByPostId(item.id)
                    item.isLiked = likeMapper.existsByCompositeId(item.id, userId)
                    item.commentCount = commentsMapper.countByPostId(item.id)
                }
                val resultJson = mutableMapOf<String, Any>()
                resultJson["list"] = lifeRecordList
                result.result = resultJson
            } else {
                result.setError(XFErrorCode.INVALID_TOKEN)
            }
            return result
        } catch (e: Error) {
            // 发生错误时，返回 500 Internal Server Error
            result.setError(XFErrorCode.SERVER_ERROR)
            return result
        }
    }

    /// 我的帖子筛选列表
    @PostMapping("/myListByFilter")
    fun getLifeRecords(@RequestBody param: Map<String, Any>, @RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            val userId = JWTManager.instance.verifyToken(token).toInt()
            val lifeRecordList: List<LifeRecordEntity>
            if (userId > 0) {
                val createTime = param["createTime"].toString().toLong()
                if (createTime < 0) {
                    result.setError(XFErrorCode.PARAM_INVALID)
                } else {
                    lifeRecordList = lifeRecordMapper.getRecordsByCreateTime(userId, createTime)
                    val resultJson = mutableMapOf<String, Any>()
                    resultJson["list"] = lifeRecordList
                    result.result = resultJson
                }
            } else {
                result.setError(XFErrorCode.INVALID_TOKEN)
            }
            return result
        } catch (e: Error) {
            // 发生错误时，返回 500 Internal Server Error
            result.setError(XFErrorCode.SERVER_ERROR)
            return result
        }
    }

    /// 帖子详情
    @PostMapping("/detail")
    fun getLifeDetail(@RequestBody param: Map<String, Any>, @RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            val userId = JWTManager.instance.verifyToken(token).toInt()
            if (userId > 0) {
                val lifeId = param["id"].toString().toLong()
                if (lifeId > 0) {
                    val lifeRecord = lifeRecordMapper.selectLifeRecordById(lifeId)
                    lifeRecord?.likedCount = likeMapper.countByPostId(lifeRecord?.id)
                    lifeRecord?.isLiked = likeMapper.existsByCompositeId(lifeRecord?.id, userId)
                    lifeRecord?.commentCount = commentsMapper.countByPostId(lifeRecord?.id)
                    result.result = lifeRecord
                    messageApi.readMessage(userId, lifeId)
                } else {
                    result.setError(XFErrorCode.PARAM_INVALID)
                }
            } else {
                result.setError(XFErrorCode.INVALID_TOKEN)
            }
            return result
        } catch (e: Error) {
            // 发生错误时，返回 500 Internal Server Error
            result.setError(XFErrorCode.SERVER_ERROR)
            return result
        }
    }

    @PostMapping("/delete")
    fun deleteLifeRecord(@RequestBody param: Map<String, Any>, @RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            val userId = JWTManager.instance.verifyToken(token).toInt()
            if (userId > 0) {
                val isExist = userMapper.checkUserExistsById(userId) > 0
                if (isExist) {
                    val list = param["list"] as? List<Int?>
                    val isDeleteImage = param["isDeleteImage"] as? Boolean ?: false
                    if (list.isNullOrEmpty()) {
                        result.setError(XFErrorCode.PARAM_INVALID)
                    } else {
                        if (isDeleteImage) {
                            /// Todo: 待优化，图片源文件没有删除
                        }
                        lifeRecordMapper.deleteLifeRecords(list)
                        spaceAPI.updateUsedSpace(userId)
                    }
                } else {
                    result.setError(XFErrorCode.INVALID_TOKEN)
                }
            } else {
                result.setError(XFErrorCode.INVALID_TOKEN)
            }
            return result
        } catch (e: Error) {
            // 发生错误时，返回 500 Internal Server Error
            result.setError(XFErrorCode.SERVER_ERROR)
            return result
        }
    }

    /// 审核
    @PostMapping("/audit")
    fun auditLifeRecord(@RequestBody param: Map<String, Any>, @RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            val status = param["status"] as? Int ?: 1
            val id = param["id"] as? Int ?: -1
            if (id <= 0) {
                result.setError(XFErrorCode.PARAM_INVALID)
                return result
            }
            val userId = JWTManager.instance.verifyToken(token).toInt()
            if (userId > 0) {
                val role: Int = userMapper.getUserRole(userId)
                if (role > 0) {
                    lifeRecordMapper.updateLifeRecordStatus(id, status)
                } else {
                    result.setError(XFErrorCode.USER_NO_PERMISSION)
                }
            } else {
                result.setError(XFErrorCode.INVALID_TOKEN)
            }

        } catch (e: Error) {
            result.setError(XFErrorCode.SERVER_ERROR)
        }
        return result
    }

    /// 编辑
    @PostMapping("/edit")
    fun editLifeRecord(@RequestBody param: Map<String, Any>, @RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            val userId = JWTManager.instance.verifyToken(token).toInt()
            if (userId > 0) {
                val lifeRecordEntity = LifeRecordEntity.fromJson(param)
                if ((lifeRecordEntity.id ?: 0) > 0) {
                    lifeRecordMapper.updateLifeRecord(lifeRecordEntity)
                } else {
                    result.setError(XFErrorCode.PARAM_INVALID)
                }
            } else {
                result.setError(XFErrorCode.INVALID_TOKEN)
            }
        } catch (e: Error) {
            result.setError(XFErrorCode.SERVER_ERROR)
        }
        return result
    }

    private fun checkLifeRecordParam(param: Map<String, Any>): Boolean {
        var  result = true
        try {
            val  title = param["title"] as? String
            val images = param["imageObjects"] as? List<String>
            if (title.isNullOrEmpty() || images.isNullOrEmpty()) {
                result = false
            }
        } catch (e: Error) {
            result = false
        }
        return result
    }

    /// 获取评论信息
    @PostMapping("/comments")
    fun getComments(@RequestBody param: Map<String, Any>, @RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            val userId = JWTManager.instance.verifyToken(token).toInt()
            val pageSize = param["pageSize"] as? Int ?: 20
            val pageIndex = param["page"] as? Int ?: 1
            if (userId > 0) {
                val lifeId = param["postId"].toString().toLong()
                if (lifeId > 0) {
                    val list = commentsMapper.getCommentsByPostId(lifeId, (pageIndex - 1) * pageSize, pageSize)
                    for (item in list) {
                        item.replyCount = commentsMapper.replyCountByCommentId(item.id)
                    }
                    result.result = mapOf("list" to list)
                } else {
                    result.setError(XFErrorCode.PARAM_INVALID)
                }
            } else {
                result.setError(XFErrorCode.INVALID_TOKEN)
            }
        } catch (e: Error) {
            result.setError(XFErrorCode.SERVER_ERROR)
        }
        return result
    }

    /// 获取回复列表
    @PostMapping("/getReplies")
    fun getReplies(@RequestBody param: Map<String, Any>, @RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            val userId = JWTManager.instance.verifyToken(token).toInt()
            val pageSize = param["pageSize"] as? Int ?: 20
            val pageIndex = param["page"] as? Int ?: 1
            if (userId > 0) {
                val parentId = param["parentId"].toString().toLong()
                if (parentId > 0) {
                    val list = commentsMapper.getRepliesByParentId(parentId, (pageIndex - 1) * pageSize, pageSize)
                    result.result = mapOf("list" to list)
                } else {
                    result.setError(XFErrorCode.PARAM_INVALID)
                }
            } else {
                result.setError(XFErrorCode.INVALID_TOKEN)
            }
        } catch (e: Error) {
            result.setError(XFErrorCode.SERVER_ERROR)
        }
        return result
    }

    /// 创建评论
    @PostMapping("/addComment")
    fun addComment(@RequestBody param: Map<String, Any>, @RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            val userId = JWTManager.instance.verifyToken(token).toInt()
            if (userId > 0) {
                val lifeId = param["postId"].toString().toLong()
                val commentJson = mutableMapOf<String, Any>()
                commentJson.putAll(param)
                commentJson["userId"] = userId
                commentJson["createdAt"] = System.currentTimeMillis()
                if (lifeId > 0) {
                    val lifeCommentsEntity = LifeCommentsEntity.fromJson(commentJson)
                    lifeCommentsEntity.content = lifeCommentsEntity.content?.let { XFSecurity.replaceSensitive(it) }
                    commentsMapper.insertComments(lifeCommentsEntity)
                    messageApi.createCommentMessage(commentJson)
                    result.result = mapOf("content" to lifeCommentsEntity.content)
                } else {
                    result.setError(XFErrorCode.PARAM_INVALID)
                }
            } else {
                result.setError(XFErrorCode.INVALID_TOKEN)
            }
        } catch (e: Error) {
            result.setError(XFErrorCode.SERVER_ERROR)
        }
        return result
    }

    /// 点赞
    @PostMapping("/likeAction")
    fun addLike(@RequestBody param: Map<String, Any>, @RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            val userId = JWTManager.instance.verifyToken(token).toInt()
            if (userId > 0) {
                val lifeId = param["postId"].toString().toLong()
                val isLike = param["isLike"].toString().toBoolean()
                val likeJson = mutableMapOf<String, Any>()
                likeJson.putAll(param)
                likeJson["userId"] = userId
                likeJson["createdAt"] = System.currentTimeMillis()
                if (isLike) {
                    val lifeLikeEntity = LifeLikeEntity.fromJson(likeJson)
                    likeMapper.insertLike(lifeLikeEntity)
                } else {
                    likeMapper.deleteLike(lifeId, userId)
                }
            } else {
                result.setError(XFErrorCode.INVALID_TOKEN)
            }
        } catch (e: Error) {
            result.setError(XFErrorCode.SERVER_ERROR)
        }
        return result
    }

    /// 创建回复
    @PostMapping("/addReply")
    fun addReply(@RequestBody param: Map<String, Any>, @RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            val userId = JWTManager.instance.verifyToken(token).toInt()
            if (userId > 0) {
                val parentId = param["parentId"].toString().toLong()
                val replyJson = mutableMapOf<String, Any>()
                replyJson.putAll(param)
                replyJson["userId"] = userId
                replyJson["createdAt"] = System.currentTimeMillis()
                if (parentId > 0) {
                    val lifeCommentsEntity = LifeCommentsEntity.fromJson(replyJson)
                    lifeCommentsEntity.content = lifeCommentsEntity.content?.let { XFSecurity.replaceSensitive(it) }
                    result.result = mapOf("content" to lifeCommentsEntity.content)
                    result.result = lifeCommentsEntity
                } else {
                    result.setError(XFErrorCode.PARAM_INVALID)
                }
            } else {
                result.setError(XFErrorCode.INVALID_TOKEN)
            }
        } catch (e: Error) {
            result.setError(XFErrorCode.SERVER_ERROR)
        }
        return result
    }
}