package com.xfposthouse.module.life_record.feedback

import com.xfposthouse.module.life_record.entity.LifeRecordEntity
import com.xfposthouse.module.life_record.feedback.mapper.FeedBackMapper
import com.xfposthouse.network.entity.XFErrorCode
import com.xfposthouse.network.entity.XFResponseEntity
import com.xfposthouse.util.CommonUtil
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/feedback")
class FeedBackApi {
    @Autowired
    lateinit var feedBackMapper: FeedBackMapper

    @PostMapping("/create")
    fun createFeedBack(@RequestBody param: Map<String, Any>): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            // 插入 LifeRecord 数据
            val lifeJson = mutableMapOf<String, Any>()
            lifeJson.putAll(param)
            lifeJson["publishTime"] = System.currentTimeMillis()
            feedBackMapper.insertFeedBack(LifeRecordEntity.fromJson(lifeJson))
            return result
        } catch (e: Error) {
            // 发生错误时，返回 500 Internal Server Error
            result.setError(XFErrorCode.SERVER_ERROR)
            return result
        }
    }

    @PostMapping("/list")
    fun listLifeRecord(@RequestBody param: Map<String, Any>): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            // 插入 LifeRecord 数据
            var lifeRecordList: List<LifeRecordEntity>
            if (param.containsKey("userId")) {
                val userId = param["userId"] as Int
                lifeRecordList = feedBackMapper.selectFeedBacksByAuthorId(userId)
            } else {
                lifeRecordList = feedBackMapper.getFeedBacks()
            }
            val resultJson = mutableMapOf<String, Any>()
            resultJson["list"] = lifeRecordList
            result.result = resultJson
            return result
        } catch (e: Error) {
            // 发生错误时，返回 500 Internal Server Error
            result.code = XFErrorCode.SERVER_ERROR.code
            result.msg = XFErrorCode.SERVER_ERROR.message
            println(e.toString())
            return result
        }
    }
}