package com.xfposthouse.mapper


import com.xfposthouse.module.space.entity.SpaceEntity
import org.apache.ibatis.annotations.*
import org.springframework.stereotype.Repository
import com.mybatisflex.core.BaseMapper

@Repository
interface SpaceMapper: BaseMapper<SpaceEntity> {

    // 插入文件记录
    @Insert("INSERT INTO files (user_id, file_name, file_url, file_size, create_time, last_modified, is_public, group_id, blog_id) " +
            "VALUES (#{userId}, #{fileName}, #{fileUrl}, #{fileSize}, #{createTime}, #{lastModified}, #{isPublic}, #{groupId}, #{blogId})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    fun insertSpaceEntity(spaceEntity: SpaceEntity): Int

    // 获取用户的所有空间文件记录
    @Select("SELECT * FROM files WHERE user_id = #{userId} AND (group_id IS NULL OR group_id = 0) LIMIT #{pageSize} OFFSET #{offset}")
    fun getUserSpaceEntities(userId: Int, pageSize: Int, offset: Int): List<SpaceEntity>?

    // 获取多用户的所有空间文件记录
    @Select("SELECT * FROM files WHERE user_id IN (#{userIds}) AND (group_id IS NULL OR group_id = 0) LIMIT #{pageSize} OFFSET #{offset}")
    fun getUsersSpaceEntities(userIds: String, pageSize: Int, offset: Int): List<SpaceEntity>?

    // 获取文件记录通过ID
    @Select("SELECT * FROM files WHERE id = #{id}")
    fun getSpaceEntityById(id: Long): SpaceEntity?

    @Select("SELECT * FROM files WHERE user_id = #{userId} AND group_id = #{folderId} LIMIT #{pageSize} OFFSET #{offset}")
    fun getSpaceEntityByFolderIdAndUserId(userId: Long, folderId: Int, pageSize: Int, offset: Int): List<SpaceEntity>?

    // 更新文件记录
    @Update("UPDATE files SET file_name = #{fileName}, file_url = #{fileUrl}, file_size = #{fileSize}, " +
            "last_modified = #{lastModified}, is_public = #{isPublic}, group_id = #{groupId} WHERE id = #{id}")
    fun updateSpaceEntity(spaceEntity: SpaceEntity): Int

    // 删除文件记录
    @Delete("DELETE FROM files WHERE id = #{id}")
    fun deleteSpaceEntity(id: Long): Int

    @Delete("DELETE FROM files WHERE blog_id = #{id}")
    fun deleteImagesWithBlogId(id: Int): Int

    // 批量删除
    @Delete(
        "<script>" +
                "DELETE FROM files WHERE id IN " +
                "<foreach item='id' collection='list' open='(' separator=',' close=')'>" +
                "#{id}" +
                "</foreach>" +
                "</script>"
    )
    fun deleteSpaceEntities(@Param("list") ids: List<Long?>?): Int

    @Select("SELECT COUNT(*) AS total_count, SUM(file_size) AS total_file_size FROM files WHERE group_id = #{groupId}")
    fun getTotalCountAndFileSize(groupId: Int): Pair<Int, Long>

    @Update("""
        <script>
        UPDATE files
        SET group_id = #{groupId}
        WHERE id IN
        <foreach collection='ids' item='id' open='(' separator=',' close=')'>
            #{id}
        </foreach>
        </script>
    """)
    fun updateGroupIdByIds(@Param("ids") ids: List<Int>, @Param("groupId") groupId: Int)

    /// 获取用户已使用存储空间
    @Select("""
        SELECT COALESCE(SUM(file_size), 0) 
        FROM files 
        WHERE user_id = #{userId}
    """)
    fun getUserUsedSpace(@Param("userId") userId: Int): Long
}

