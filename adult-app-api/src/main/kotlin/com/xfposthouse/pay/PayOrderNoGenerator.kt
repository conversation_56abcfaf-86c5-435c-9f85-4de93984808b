package com.xfposthouse.pay

import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.*

class PayOrderNoGenerator {
    companion object {
        private val dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss")
        fun generateTransactionId(businessType: String): String {
            val timestamp = LocalDateTime.now().format(dateTimeFormatter)
            val rand = Random()
            val randomNumber = rand.nextInt(100000) + 1
            return "$businessType$timestamp$randomNumber"
        }
    }

}