server:
  port: 8086

spring:
  datasource:
    url: ****************************************************************************************************************************
    password: zqx19930304
    username: root
    driver-class-name: com.mysql.cj.jdbc.Driver
  servlet:
    multipart:
      enabled: true
      max-file-size: 10MB
      max-request-size: 10MB

logging:
  config: classpath:logback-spring.xml
  # file:
  #   path: ./logs
  # logback:
  #   rollingpolicy:
  #     max-file-size: 10MB
  #     max-history: 30
  # pattern:
  #   console: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n'

# 备用配置（已注释）
# server:
#   port: 8086
# spring:
#   datasource:
#     url: ***************************************************************************************************************************************************************************
#     password: zqx19930304
#     # password: Zqx19930304