package com.xfposthouse.module.space.entity

class SpaceEntity(
        val id: Int? = null,
        var userId: Int,
        val fileName: String,
        val fileUrl: String,
        val fileSize: Long,
        var createTime: Long,
        val lastModified: Long,
        val isPublic: Boolean = false,
        var groupId: Int,
        var blogId: Long? = null
) {
    companion object {
        // 使用 Map 来转换为 SpaceEntity
        fun fromJson(map: Map<String, Any>): SpaceEntity {
            val fileSizeLong: Long = (map["fileSize"]?:"0").toString().toLong()
            val createTime: Long = (map["createTime"]?:"0").toString().toLong()
            return SpaceEntity(
                id = map["id"] as? Int,  // 这里使用 Long 类型并允许 null
                userId = map["userId"] as? Int ?: 0,  // 默认值为 0L
                fileName = map["fileName"] as? String ?: "",
                fileUrl = map["fileUrl"] as? String ?: "",
                fileSize = fileSizeLong,  // 默认值为 0L
                createTime = createTime,  // 默认值为 0L
                lastModified = map["lastModified"] as? Long ?: 0L,  // 默认值为 0L
                isPublic = map["isPublic"] as? Boolean ?: false,  // 默认值为 false
                groupId = map["groupId"] as? Int ?: 0,
                blogId = map["blogId"] as? Long
            )
        }
    }
}