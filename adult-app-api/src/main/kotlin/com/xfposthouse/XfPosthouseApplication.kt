package com.xfposthouse

import org.mybatis.spring.annotation.MapperScan
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.runApplication

@SpringBootApplication(scanBasePackages = ["com.xfposthouse", "discipline"])
@MapperScan(value = ["com.xfposthouse.mapper", "com.xfposthouse.module.**.mapper"])
class XfPosthouseApplication

fun main(args: Array<String>) {
    runApplication<XfPosthouseApplication>(*args)
    println("启动成功")
}
