package com.xfposthouse.module.ledger

import com.mybatisflex.annotation.Column
import com.mybatisflex.annotation.Id
import com.mybatisflex.annotation.KeyType
import com.mybatisflex.annotation.Table
import java.math.BigDecimal
import java.time.LocalDateTime

@Table("ledger_book")
open class LedgerBookEntity (

    @Id(keyType = KeyType.Auto)
    var id: Long? = null,

    var userId: Long = 0,

    var name: String? = null,

    var description: String? = null,

    var currency: String? = "CNY",

    var incomeAmount: BigDecimal = BigDecimal.ZERO,

    var outAmount: BigDecimal = BigDecimal.ZERO,

    @Column("created_at")
    var createdAt: LocalDateTime = LocalDateTime.now(),

    @Column("updated_at")
    var updatedAt: LocalDateTime = LocalDateTime.now(),
)