package com.xfposthouse.security
import java.io.File

class XFSecurity {

    var filter: SensitiveFilter? = null

    companion object {
        private val shared = XFSecurity()

        fun replaceSensitive(text: String): String {
            shared.initFilter()
            /// 手机号检测替换
            var result = shared.filter?.replacePhone(text)
            /// 敏感词检测替换
            result = result?.let { shared.filter?.replaceSensitive(it) } ?: ""
            return result
        }
    }

    private fun initFilter() {
        if (filter == null) {
            val filePath = "./security.txt"
            val words = File(filePath).readLines().map { it.trim() }.toSet()
            if (words.isNotEmpty()) {
                filter = SensitiveFilter(words)
            }
        }
    }

}

class SensitiveFilter(private val sensitiveWords: Set<String>) {

    private val root = TrieNode()

    init {
        buildTrie()
    }

    // Trie 节点定义
    private class TrieNode {
        val children: MutableMap<Char, TrieNode> = mutableMapOf()
        var isEndOfWord: Boolean = false
    }

    // 构建敏感词 Trie 树
    private fun buildTrie() {
        for (word in sensitiveWords) {
            var current = root
            for (char in word) {
                current = current.children.computeIfAbsent(char) { TrieNode() }
            }
            current.isEndOfWord = true
        }
    }

    // 检测文本是否包含敏感词
    fun containsSensitive(text: String): Boolean {
        for (i in text.indices) {
            var current = root
            for (j in i until text.length) {
                val char = text[j]
                val next = current.children[char] ?: break
                if (next.isEndOfWord) return true
                current = next
            }
        }
        return false
    }

    // 返回所有命中的敏感词
    fun findAllSensitive(text: String): Set<String> {
        val result = mutableSetOf<String>()
        for (i in text.indices) {
            var current = root
            var sb = StringBuilder()
            for (j in i until text.length) {
                val char = text[j]
                val next = current.children[char] ?: break
                sb.append(char)
                if (next.isEndOfWord) result.add(sb.toString())
                current = next
            }
        }
        return result
    }

    // 替换敏感词
    fun replaceSensitive(text: String, mask: Char = '*'): String {
        val result = text.toCharArray()
        for (i in text.indices) {
            var current = root
            var j = i
            while (j < text.length) {
                val char = text[j]
                val next = current.children[char] ?: break
                if (next.isEndOfWord) {
                    for (k in i..j) {
                        result[k] = mask
                    }
                }
                current = next
                j++
            }
        }
        return String(result)
    }

    // 替换手机号
    fun replacePhone(text: String): String {
        // 手机号正则：以1开头的11位数字（常见中国手机号）
        val regex = Regex("""(?<!\d)(1\d{2})\d{4}(\d{4})(?!\d)""")
        return regex.replace(text) {
            val prefix = it.groupValues[1]
            val suffix = it.groupValues[2]
            "$prefix****$suffix"
        }
    }
}
