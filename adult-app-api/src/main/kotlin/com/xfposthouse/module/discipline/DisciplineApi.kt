package com.xfposthouse.module.discipline

import com.xfposthouse.jwt.JWTManager
import com.xfposthouse.module.discipline.param.DisciplineCreateParam
import com.xfposthouse.network.entity.XFErrorCode
import com.xfposthouse.network.entity.XFResponseEntity
import discipline.DisciplineServer
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/discipline")
class DisciplineApi(
    private val disciplineService: DisciplineServer
) {
    // 创建
    @PostMapping
    fun create(@RequestBody param: DisciplineCreateParam, @RequestHeader("token") token: String): XFResponseEntity {
        val userId = JWTManager.instance.verifyToken(token).toInt()
        val result = XFResponseEntity()
        if (userId > 0) {
            if (checkCreateParam(param)) {
                val isSuccess = param.disciplinePlanEntity?.let { disciplineService.createPlan(it) }
                if (isSuccess == true) {
                    param.disciplineNodes?.let { disciplineService.createPlanNodes(it) }
                } else {
                    result.setError(XFErrorCode.SERVER_ERROR)
                }
            } else {
                result.setError(XFErrorCode.PARAM_INVALID)
            }
        } else {
            result.setError(XFErrorCode.INVALID_TOKEN)
        }
        return result
    }

    /** 检验创建参数 */
    fun checkCreateParam(param: DisciplineCreateParam): Boolean {
        if (param.disciplinePlanEntity == null) {
            return false
        }
        if (param.disciplineNodes?.isEmpty() == true) {
            return false
        }
        return true
    }

 }