package com.xfposthouse.jwt

import com.auth0.jwt.JWT
import com.auth0.jwt.JWTVerifier
import com.auth0.jwt.algorithms.Algorithm
import com.auth0.jwt.interfaces.DecodedJWT
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.*


class JWTManager {

    companion object {
        val instance: JWTManager = JWTManager()
    }

    private val SECREAT_KEY: String = "SJLDHFKSK"

    private val JWT_ISSURE: String = "ZXZZLZQX"

    /// 获取token
    fun createJWT(userId: String, pwd: String): String {
        var token:String = ""
        try {
            val expireAt: Date =
                Date.from(LocalDateTime.now().plusHours(30 * 24).atZone(ZoneId.systemDefault()).toInstant())
            token = JWT.create().withIssuer(JWT_ISSURE)
                .withClaim("userId", userId)
                .withClaim("pwd", pwd)
                .withExpiresAt(expireAt).sign(Algorithm.HMAC256(SECREAT_KEY))
        } catch (error: Error) {
            println(error)
        }
        return token
    }

    /// 校验token
    fun verifyToken(token: String): String {
        var userId = "-1"
        try {
            val ver = JWT.require(Algorithm.HMAC256(SECREAT_KEY)).withIssuer(JWT_ISSURE).build()
            val jwt: DecodedJWT = ver.verify(token)
            userId = jwt.getClaim("userId").asString()
        } catch (error: Error) {
            println(error)
        }
        return userId
    }
}