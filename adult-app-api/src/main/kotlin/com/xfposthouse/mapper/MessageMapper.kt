package com.xfposthouse.mapper

import com.xfposthouse.module.message.MessageEntity
import org.apache.ibatis.annotations.*
import com.mybatisflex.core.BaseMapper

@Mapper
interface MessageMapper: BaseMapper<MessageEntity> {

    @Insert("INSERT INTO messages (receiver_id, from_id, refer_id, content, message_type, is_read, created_at, updated_at) VALUES (#{receiverId}, #{fromId},#{referId}, #{content}, #{messageType}, #{isRead}, #{createdAt}, #{updatedAt})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    fun insertMessage(message: MessageEntity): Int

    @Select("SELECT * FROM messages WHERE id = #{id}")
    @Results(
        id = "messageMap", value = [
            Result(property = "id", column = "id"),
            Result(property = "receiverId", column = "receiver_id"),
            Result(property = "referId", column = "refer_id"),
            Result(property = "fromId", column = "from_id"),
            Result(property = "content", column = "content"),
            Result(property = "messageType", column = "message_type"),
            Result(property = "isRead", column = "is_read"),
            Result(property = "createdAt", column = "created_at"),
            Result(property = "updatedAt", column = "updated_at"),
        ]
    )
    fun findById(id: Long): MessageEntity?

    @Select("SELECT COUNT(*) FROM messages WHERE receiver_id = #{receiverId} AND is_read = false")
    fun findUnReadMessageCount(receiverId: Int): Int

    @Update("UPDATE messages SET content = #{content}, message_type = #{messageType}, is_read = #{isRead}, updated_at = #{updatedAt} WHERE id = #{id}")
    fun update(message: MessageEntity): Int

    @Delete("DELETE FROM messages WHERE id = #{id}")
    fun deleteById(id: Long): Int

    @Select("SELECT * FROM messages WHERE receiver_id = #{userId} ORDER BY created_at DESC LIMIT #{pageSize} OFFSET #{offset}")
    @ResultMap("messageMap")
    fun findMessagesByUserId(userId: Int, pageSize: Int, offset: Int): List<MessageEntity>

    @Update("UPDATE messages SET is_read = true WHERE refer_id = #{referId} AND receiver_id = #{userId}")
    fun updateIsRead(userId: Int, referId: Long): Int
}