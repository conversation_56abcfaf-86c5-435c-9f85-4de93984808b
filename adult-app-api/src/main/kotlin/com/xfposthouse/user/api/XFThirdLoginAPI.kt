package com.xfposthouse.user.api

import com.xfposthouse.mapper.UserMapper
import com.xfposthouse.module.ledger.LedgerAPI
import com.xfposthouse.module.ledger.LedgerAddBookParam
import com.xfposthouse.network.entity.XFErrorCode
import com.xfposthouse.network.entity.XFResponseEntity
import com.xfposthouse.user.entity.ThirdUserParam
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/user")
class XFThirdLoginAPI {

    @Autowired
    lateinit var userMapper: UserMapper

    /// 微信Api
    @Autowired
    lateinit var wechatUserAPI: WechatUserAPI

    @PostMapping("/loginWx")
    fun loginWithWeiXin(@RequestBody thirdLogin: ThirdUserParam) : XFResponseEntity {
        var result = XFResponseEntity()
        try {
            if (thirdLogin.type == 3) {
                result = wechatUserAPI.login(thirdLogin.code?:"")
                return result
            }
            result.setError(XFErrorCode.USER_LOGIN_TYPE_ERROR)
            return result
        } catch (error: Error) {
            result.code = -1000
            result.msg = error.toString()
            return result
        }
    }
}