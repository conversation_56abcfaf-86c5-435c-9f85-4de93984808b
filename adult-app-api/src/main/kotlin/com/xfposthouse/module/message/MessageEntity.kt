package com.xfposthouse.module.message

open class MessageEntity {

    val id: Long = 0L
    var receiverId: Int? = null
    var referId: Long? = null
    var content: String? = null
    var fromId: Int? = null
    /// 1 评论
    var messageType: Int? = null
    var isRead: Boolean = false
    var createdAt: Long? = null
    var updatedAt: Long? = null
    companion object {
        fun fromJson(json: Map<String, Any>): MessageEntity {
            val result = MessageEntity();
            result.receiverId = json.parseInt("receiverId", required = true)!!;
            result.content = json.parseString("content")
            result.content = json.parseString("content")
            result.referId = json.parseNullableLong("referId")
            result.fromId = json.parseInt("fromId", required = true)
            result.messageType = json.parseInt("messageType", required = true)!!
            result.isRead = json.parseBoolean("isRead") ?: false
            result.createdAt = json.parseNullableLong("createdAt")
            result.updatedAt = json.parseNullableLong("updatedAt")
            return result
        }

        // 扩展函数：解析Int类型（强制要求字段存在）
        private fun Map<String, Any>.parseInt(key: String, required: Boolean): Int? {
            return when (val value = this[key]) {
                is Number -> value.toInt()
                is String -> value.toIntOrNull()
                else -> if (required) throw IllegalArgumentException("Missing required field: $key") else null
            }
        }

        // 扩展函数：解析Long类型（可空）
        private fun Map<String, Any>.parseNullableLong(key: String): Long? {
            return this[key]?.toString()?.toLongOrNull()
        }

        // 扩展函数：解析String类型
        private fun Map<String, Any>.parseString(key: String): String {
            return this[key] as? String
                    ?: throw IllegalArgumentException("Field $key is not a String or is missing")
        }

        // 扩展函数：解析Boolean类型（可空）
        private fun Map<String, Any>.parseBoolean(key: String): Boolean? {
            return when (val value = this[key]) {
                is Boolean -> value
                is String -> value.toBooleanStrictOrNull()
                else -> null
            }
        }
    }
}