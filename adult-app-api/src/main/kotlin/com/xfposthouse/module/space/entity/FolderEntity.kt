package com.xfposthouse.module.space.entity

class FolderEntity(
    var id: Int? = null, // 文件夹唯一标识，主键，自增
    var userId: Int?, // 文件夹所属用户
    var name: String?, // 文件夹名称
    var createTime: Long?, // 文件夹创建时间
    var lastModified: Long? // 文件夹最后修改时间
) {
    companion object {
        fun fromJson(json: Map<String, Any>): FolderEntity {
            val lastModified: Long = (json["lastModified"]?:"0").toString().toLong()
            val createTime: Long = (json["createTime"]?:"0").toString().toLong()
            return FolderEntity(
                userId = json["userId"] as Int?, // 强制转换为 Int
                name = json["name"] as String?, // 强制转换为 String
                createTime = createTime, // 强制转换为 Long
                lastModified = lastModified
            )
        }
    }
}