package com.xfposthouse.user.entity

import com.mybatisflex.annotation.Column
import com.mybatisflex.annotation.Table
import java.time.LocalDateTime

/**
 * 微信用户表
 * <AUTHOR>
 * @since: 2025-06-23
 */

/// 86#$@68
const val Default_Password = "ac74a942d5e2e999768172a1d1cf3385"

@Table(value = "user_oauth_bindings")
data class ThirdUserEntity (

    /**
     * 自增id
     */
    var id: Long = 0,

    /**
     * 用户id
     */
    var userId: Int = 0,

    /**
     * openid
     */
    var openid: String? = "",

    /**
     * unionid
     */
    var unionid: String? = "",

    /**
     * bindStatus,绑定状态
     */
    @Column("bind_status")
    var bindStatus: Boolean = false,

    /**
     * accessToken
     */
    @Column("access_token")
    var accessToken: String? = "",

    /**
     * refreshToken
     */
    @Column("refresh_token")
    var refreshToken: String? = "",

    /**
    * 3, 微信
    * */
    var type: Int = 0,

    /**
     * 创建时间
     */
    @Column("created_at")
    var createdAt: LocalDateTime = LocalDateTime.now(),

    /**
     * 更新时间
     */
    @Column("updated_at")
    var updateAt: LocalDateTime = LocalDateTime.now(),

    /**
     * 三方授权 nickname
     */
    @Column(ignore = true)
    var nickname: String? = "",

    /**
     * 三方授权 sex
     */
    @Column(ignore = true)
    var sex: Boolean? = false,

    /**
     * 三方授权 sex
     */
    @Column(ignore = true)
    var avatar: String? = "",

    )

