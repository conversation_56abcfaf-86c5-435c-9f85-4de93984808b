package com.xfposthouse.module.config

import com.xfposthouse.mapper.ConfigMapper
import com.xfposthouse.network.entity.XFErrorCode
import com.xfposthouse.network.entity.XFResponseEntity
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api")
class AdultConfigApi {
    @Autowired
    lateinit var configMapper: ConfigMapper

    @GetMapping("adult/config")
    fun getConfig(): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            result.result = configMapper.getConfig()
        } catch (e: Error) {
            result.setError(XFErrorCode.SERVER_ERROR)
        }
        return result
    }
}