package com.xfposthouse.util

class CommonUtil {

    companion object {

        fun entityToMap(obj: Any): MutableMap<String, Any> {
            val map: MutableMap<String, Any> = mutableMapOf()

            for (field in obj.javaClass.declaredFields) {
                try {
                    field.isAccessible = true
                    val value = field.get(obj)
                    val key = field.name
                    if (value != null) {
                        map[key] = value
                    }
                } catch (err: Exception) {
                    err.printStackTrace()
                }
            }
            return map
        }

        /// 数据查询list转换
        fun listToString(list: List<Any>): String {
            var str: String = ""
            for (item in list) {
                str += "$item,"
            }
            if (str.endsWith(",")) {
                str = str.substring(0, str.length - 1)
            }
            return str
        }

        /// 格式化压缩为一行
        fun formatLineString(str: String?): String {
            return str?.replace(Regex("\\s+"), " ") ?: ""
        }
    }
}