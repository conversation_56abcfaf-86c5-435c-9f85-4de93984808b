package discipline

import om.xfposthouse.entity.DisciplineNodeEntity
import om.xfposthouse.entity.DisciplinePlanEntity
import om.xfposthouse.mapper.DisciplineMapper
import om.xfposthouse.mapper.DisciplineNodeMapper
import org.springframework.stereotype.Service

@Service
class DisciplineServer(
    private val planMapper: DisciplineMapper,
    private val nodeMapper: DisciplineNodeMapper,
) {
    fun createPlan(plan: DisciplinePlanEntity): Boolean {
        return planMapper.insert(plan) > 0
    }

    fun createPlanNodes(plans: List<DisciplineNodeEntity>): Boolean {
        return nodeMapper.insertBatch(plans) > 0
    }
}