package com.xfposthouse.mapper

import com.xfposthouse.module.family.FamilyEntity
import org.apache.ibatis.annotations.*
import org.springframework.stereotype.Repository

@Repository
interface ConfigMapper: BaseMapper<ConfigEntity>  {

    @Select("SELECT * FROM adult_config LIMIT 1")
    @Results(
        id = "configMap", // 为结果映射定义一个名称
        value = [
            Result(property = "releaseVersion", column = "release_version"),
            Result(property = "isForceUpdate", column = "is_force_update"),
            Result(property = "hasCommunity", column = "has_community"),
        ]
    )
    fun getConfig(): ConfigEntity?
}

data class ConfigEntity(
    val releaseVersion: String? = null,
    val isForceUpdate: Boolean = false,
    val hasCommunity: Boolean = false
)