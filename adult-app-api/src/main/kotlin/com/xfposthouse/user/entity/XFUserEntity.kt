package com.xfposthouse.user.entity

data class XFUserEntity(
    var userId: Int = 0,
    var nickName: String = "",
//    var passWord: String = "",
    var userName: String = "",
    var level: Int = 0,
    var role: Int = 0, // 0: 普通用户, 1: 管理员, 2: 超级管理员
    var registerTime: Long = 0L,
    var sex: Int = 0,
    var tags: MutableList<String> = mutableListOf(),
    var chatCount: Int = 0,
    var fiveStarCommentCount: Int = 0,
    var badCommentCount: Int = 0,
    var isVerified: Boolean = false,
    var avatar: String = "",
    var introduction: String = "",
    var userState: Int = 0,  // 用户状态，1表示正常，0表示禁用，默认正常, 3,注销中’
    var usedSpaceSize: Long = 0L,
    var totalSpaceSize: Long = 0L,
    var totalVoiceCount: Int = 0,
    var usedVoiceCount: Int = 0,
    var birthDay: Long = 0L,
    // 会员信息
    var membershipId: Int? = null,  // 会员 ID（外键）
    var membershipStartTime: Long? = null,  // 会员开始时间（时间戳）
    var membershipEndTime: Long? = null,  // 会员到期时间（时间戳）
    var membershipLevel: Int = 0,  // 会员等级（默认 0，无会员）

    /// 家庭id
    var familyId: Int? = null,
    /// 手机号
    var phoneNumber: String? = null,
)

data class XFUserTokenEntity(
    var userInfo: XFUserEntity? = null,
    var token: String = ""
)
