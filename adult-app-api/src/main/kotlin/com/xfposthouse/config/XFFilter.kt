package com.xfposthouse.config

import jakarta.servlet.Filter
import jakarta.servlet.FilterChain
import jakarta.servlet.ServletRequest
import jakarta.servlet.ServletResponse
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.springframework.stereotype.Component
import org.springframework.web.util.ContentCachingRequestWrapper
import org.springframework.web.util.ContentCachingResponseWrapper

@Component
class RequestResponseWrapperFilter : Filter {
    override fun doFilter(
        request: ServletRequest,
        response: ServletResponse,
        chain: FilterChain
    ) {
        val reqWrapper = ContentCachingRequestWrapper(request as HttpServletRequest)
        val resWrapper = ContentCachingResponseWrapper(response as HttpServletResponse)

        try {
            chain.doFilter(reqWrapper, resWrapper)
        } finally {
            resWrapper.copyBodyToResponse()
        }
    }
}