package com.xfposthouse.module.vip

import com.xfposthouse.jwt.JWTManager
import com.xfposthouse.mapper.UserMapper
import com.xfposthouse.network.entity.XFErrorCode
import com.xfposthouse.network.entity.XFResponseEntity
import com.xfposthouse.module.vip.entity.BenefitType
import com.xfposthouse.module.vip.entity.MemberShipEntity
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*
import com.xfposthouse.module.vip.mapper.MemberShipMapper
import com.xfposthouse.module.vip.entity.MembershipBenefitEntity
import com.xfposthouse.module.vip.entity.MembershipBenefitRelationEntity
import com.xfposthouse.module.vip.mapper.MembershipBenefitMapper
import com.xfposthouse.module.vip.mapper.MembershipBenefitRelationMapper
import org.springframework.transaction.annotation.Transactional

@RestController
@RequestMapping("/api/membership")
class MemberShipApi {

    @Autowired
    lateinit var membershipMapper: MemberShipMapper

    @Autowired
    lateinit var membershipBenefitMapper: MembershipBenefitMapper

    @Autowired
    lateinit var membershipBenefitRelationMapper: MembershipBenefitRelationMapper

    @Autowired
    lateinit var userMapper: UserMapper

    @PostMapping("/create")
    @Transactional
    fun create(@RequestBody param: Map<String, Any>, @RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()

        try {
            val userId = JWTManager.instance.verifyToken(token)
            val user = userMapper.getUserByUserId(userId.toInt())
            if ((user?.role?:0) > 0) {
                val memberShipEntity = MemberShipEntity.fromJson(param)
                val benefitIds = param["benefitIds"] as? List<Int> ?: emptyList()
                if (benefitIds.isEmpty()) {
                    result.setError(XFErrorCode.PARAM_INVALID)
                } else {
                    membershipMapper.insertMemberShip(memberShipEntity)
                    benefitIds.forEach { benefitId ->
                        val entity = MembershipBenefitRelationEntity()
                        entity.benefitId = benefitId
                        entity.membershipId = memberShipEntity.membershipId
                        membershipBenefitRelationMapper.insertRelation(entity)
                    }
                }

                return result
            } else {
                result.setError(XFErrorCode.USER_NO_PREMISS)
                return result
            }
            return result
        } catch (e: Error) {
            // 发生错误时，返回 500 Internal Server Error
            result.code = XFErrorCode.SERVER_ERROR.code
            result.msg = XFErrorCode.SERVER_ERROR.message
            return result
        }
    }

    @PostMapping("/list")
    fun list(@RequestBody param: Map<String, Any>): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            val resultJson = mutableMapOf<String, Any>()
            resultJson["list"] = membershipMapper.findAll()
            result.result = resultJson
            return  result
        } catch (e: Error) {
            result.code = XFErrorCode.SERVER_ERROR.code
            result.msg = XFErrorCode.SERVER_ERROR.message
            return result
        }
    }

    /// 创建会员权益
    @PostMapping("/createBenefit")
    fun createBenefit(@RequestBody param: Map<String, Any>, @RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            val userId = JWTManager.instance.verifyToken(token)
            val user = userMapper.getUserByUserId(userId.toInt())
            if ((user?.role?:0) > 0) {
                val benefits = param["list"] as? List<Map<String, Any>> ?: emptyList()
                if (benefits.isEmpty()) {
                    result.setError(XFErrorCode.PARAM_INVALID)
                } else {
                    benefits.forEach { benefitJson ->
                        val benefitEntity = MembershipBenefitEntity.fromJson(benefitJson)
                        membershipBenefitMapper.insertBenefit(benefitEntity)
                    }
                }
            } else {
                result.setError(XFErrorCode.USER_NO_PREMISS)
            }
            return result
        } catch (e: Error) {
            result.setError(XFErrorCode.SERVER_ERROR)
        }
        return result
    }

    fun buyMemberShipSuccess(userId: Int, memberShipId: Int) {
        val memberShipEntity = membershipMapper.findById(memberShipId)
        val benefits = memberShipEntity?.benefits ?: emptyList()
        if (benefits.isNotEmpty()) {
            val nowTime = System.currentTimeMillis()
            val user = userMapper.getUserByUserId(userId)
            for (benefitEntity in benefits) {
                if (benefitEntity.benefitType == BenefitType.SPACE.code) {
                    user?.totalSpaceSize = benefitEntity.benefitValue?.toLong()?:0
                } else if (benefitEntity.benefitType == BenefitType.VOICE_COUNT.code) {
                    val originTotalCount = user?.totalVoiceCount?:0
                    user?.totalVoiceCount = (benefitEntity.benefitValue?.toInt()?:0) + originTotalCount
                }
            }
            if (user != null) {
                val endTime = user.membershipEndTime ?:  nowTime
                user.membershipId = memberShipId
                user.membershipLevel = memberShipEntity?.membershipLevel?:0
                user.membershipStartTime = nowTime
                user.membershipEndTime = endTime + ((memberShipEntity?.durationDays?:0).toLong() * 24 * 3600 * 1000)
                userMapper.updateUser(user)
            }
        }
    }

    @GetMapping("/vipProtocol")
    fun getVIPProtocol(): XFResponseEntity {
        val result = XFResponseEntity()
        result.result = VIP_SERVER_PROTOCOL
        return result
    }
}

const val VIP_SERVER_PROTOCOL = "会员服务协议\n" +
        "欢迎使用[成人记App]会员服务！在使用本服务之前，请仔细阅读以下条款。使用本服务即表示您已阅读、理解并同意接受本协议的所有条款。\n" +
        "一、会员服务概述\n" +
        "[成人记App]提供多种会员服务，包括但不限于私有云存储空间和语音记账功能。会员服务分为以下几种类型：\n" +
        "普通会员：享受普通会员服务。\n" +
        "黄金会员：高级会员，享受无限存储空间、优先客服支持及其他特权。\n" +
        "二、会员权益\n" +
        "私有云存储空间：\n" +
        "普通月度会员：获取私有云存储空间和语音记账次数。\n" +
        "普通季度会员：获取私有云存储空间和语音记账次数。\n" +
        "普通年会员：获取私有云存储空间和语音记账次数。\n" +
        "黄金月度会员：获取更多私有云存储空间和语音记账次数。\n" +
        "黄金季度会员：获取更多私有云存储空间和语音记账次数。\n" +
        "黄金年会员：获取更多私有云存储空间和语音记账次数。\n" +
        "其他特权：\n" +
        "黄金会员享有专属客服支持。\n" +
        "季度会员和年会员享有优先体验新功能的权益。\n" +
        "三、会员费用\n" +
        "根据运营情况，通过后台配置具体会员费用。\n" +
        "会员费用需通过App内支付完成，支付方式包括但不限于支付宝、微信支付等。\n" +
        "四、会员服务期限\n" +
        "月度会员：自支付之日起，有效期为30天。\n" +
        "季度会员：自支付之日起，有效期为90天。\n" +
        "年会员：自支付之日起，有效期为365天。\n" +
        "会员服务期限届满后，用户需重新订阅以继续享受会员服务。\n" +
        "五、会员服务的变更与取消\n" +
        "服务变更：\n" +
        "我们可能会根据业务需要调整会员服务的内容、价格或使用规则。如有重大变更，我们将通过App内通知，电子邮件或短信提前通知您。\n" +
        "如果您不同意变更内容，可以在变更生效前取消会员服务。\n" +
        "取消会员服务：\n" +
        "暂不支持App内取消，若确实需要取消，请联系客服。\n" +
        "六、数据安全与隐私\n" +
        "我们将严格保护您的个人信息和数据安全，不会向第三方披露您的数据，除非：\n" +
        "法律要求；\n" +
        "维护我们的合法权益；\n" +
        "维护社会公共利益。\n" +
        "您应妥善保管您的账号和密码，避免信息泄露。因您个人原因导致的数据泄露，我们不承担任何责任。\n" +
        "七、知识产权\n" +
        "本App的所有内容，包括但不限于文字、图片、音频、视频、软件代码等，均受版权保护，未经我们书面许可，不得复制、传播或用于其他商业用途。\n" +
        "您在本App上传或存储的数据均归您所有，同时您授权我们基于为您提供服务的目的使用这些数据。\n" +
        "八、违约与责任限制\n" +
        "如您违反本协议的任何条款，我们有权暂停或终止您的会员服务，并保留追究违约责任的权利。\n" +
        "我们不承担因不可抗力、网络故障、第三方行为等原因导致的服务中断或数据丢失的责任。\n" +
        "九、争议解决\n" +
        "本协议的解释和适用均受中华人民共和国法律管辖。如发生争议，双方应友好协商解决；协商不成的，任何一方均可向我们所在地的人民法院提起诉讼。\n" +
        "十、其他\n" +
        "本协议的任何条款被认定为无效或不可执行，不影响其他条款的效力。\n" +
        "如有任何疑问或需要帮助，请通过App内的客服功能联系我们。\n" +
        "感谢您选择[成人记]！我们致力于为您提供优质的服务体验。"