package com.xfposthouse.upload

import com.xfposthouse.network.entity.XFErrorCode
import com.xfposthouse.network.entity.XFResponseEntity
import com.qcloud.cos.COSClient
import com.qcloud.cos.ClientConfig
import com.qcloud.cos.auth.BasicCOSCredentials
import com.qcloud.cos.auth.COSCredentials
import com.qcloud.cos.model.DeleteObjectsRequest
import com.qcloud.cos.model.DeleteObjectsRequest.KeyVersion
import com.qcloud.cos.region.Region
import com.tencent.cloud.CosStsClient
import com.tencent.cloud.Policy
import com.tencent.cloud.Statement
import com.tencent.cloud.cos.util.Jackson
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import java.io.File
import java.io.IOException
import java.util.*
import kotlin.collections.ArrayList


@RestController
@RequestMapping("/api/cos")
class FileUploadController {

    var client: COSClient? = null

    @GetMapping("/getSignCode")
    fun getCosSign(@RequestBody param: Map<String, Any>): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            result.result = getTempSecret()
        } catch (e: Exception) {
            result.code = XFErrorCode.SERVER_ERROR.code
            result.msg = XFErrorCode.SERVER_ERROR.message
            e.printStackTrace()
        }
        return result
    }

    @PostMapping("/upload")
    fun uploadFile(@RequestParam("file") file: MultipartFile): String? {
        return if (file.isEmpty) {
            "Please select a file to upload."
        } else try {
            // 获取文件名
            val fileName = file.originalFilename
            // 获取文件的保存路径（这里保存在当前项目的根目录下）
            val filePath: String = (File("").absolutePath + File.separator).toString() + fileName
            // 将文件保存到指定路径
            file.transferTo(File(filePath))
            "File uploaded successfully!"
        } catch (e: IOException) {
            e.printStackTrace()
            "File upload failed."
        }
    }

    @PostMapping("/delete")
    fun deleteFiles(fileKeys: List<String>): Boolean {
        if (client == null) {
            client = createCOSClient()
        }
        val deleteObjectsRequest = DeleteObjectsRequest("file-xfpost-1317756102")

        // 设置要删除的key列表, 最多一次删除1000个
        val keyList: ArrayList<KeyVersion> = ArrayList()
        fileKeys.forEach {
            // 传入要删除的文件名
            // 注意文件名不允许以正斜线/或者反斜线\开头，例如：
            // 存储桶目录下有a/b/c.txt文件，如果要删除，只能是 keyList.add(new KeyVersion("a/b/c.txt")),
            // 若使用 keyList.add(new KeyVersion("/a/b/c.txt"))会导致删除不成功
            keyList.add(KeyVersion(it))
        }
        deleteObjectsRequest.keys = keyList
        client?.deleteObjects(deleteObjectsRequest)
        return false
    }

    private fun getTempSecret(): Map<String, Any> {
        val config = TreeMap<String, Any>()
        try {
            //这里的 SecretId 和 SecretKey 代表了用于申请临时密钥的永久身份（主账号、子账号等），子账号需要具有操作存储桶的权限。
            val secretId = "AKIDKer296uVv9OYXJzhQ5auIftzvhHfqwra" //用户的 SecretId，建议使用子账号密钥，授权遵循最小权限指引，降低使用风险。子账号密钥获取可参见 https://cloud.tencent.com/document/product/598/37140
            val secretKey = "Zv88xaFk27zkLlzzpRt8C6n24wb3JVZh" //用户的 SecretKey，建议使用子账号密钥，授权遵循最小权限指引，降低使用风险。子账号密钥获取可参见 https://cloud.tencent.com/document/product/598/37140
            // 替换为您的云 api 密钥 SecretId
            config["secretId"] = secretId
            // 替换为您的云 api 密钥 SecretKey
            config["secretKey"] = secretKey

            // 初始化 policy
            val policy = Policy()

            // 设置域名:
            // 如果您使用了腾讯云 cvm，可以设置内部域名
            //config.put("host", "sts.internal.tencentcloudapi.com");

            // 临时密钥有效时长，单位是秒，默认 1800 秒，目前主账号最长 2 小时（即 7200 秒），子账号最长 36 小时（即 129600）秒
            config["durationSeconds"] = 1800
            // 换成您的 bucket
            config["bucket"] = "file-xfpost-1317756102"
            // 换成 bucket 所在地区
            config["region"] = "ap-shanghai"

            // 开始构建一条 statement
            val statement = Statement()
            // 声明设置的结果是允许操作
            statement.setEffect("allow")
            /**
             * 密钥的权限列表。必须在这里指定本次临时密钥所需要的权限。
             * 权限列表请参见 https://cloud.tencent.com/document/product/436/31923
             * 规则为 {project}:{interfaceName}
             * project : 产品缩写  cos相关授权为值为cos,数据万象(数据处理)相关授权值为ci
             * 授权所有接口用*表示，例如 cos:*,ci:*
             * 添加一批操作权限 :
             */
            statement.addActions(
                arrayOf(
                    "cos:PutObject",  // 表单上传、小程序上传
                    "cos:PostObject",  // 分块上传
                    "cos:InitiateMultipartUpload",
                    "cos:ListMultipartUploads",
                    "cos:ListParts",
                    "cos:UploadPart",
                    "cos:CompleteMultipartUpload",  // 处理相关接口一般为数据万象产品 权限中以ci开头
                    // 创建媒体处理任务
                    "ci:CreateMediaJobs",  // 文件压缩
                    "ci:CreateFileProcessJobs"
                )
            )

            /**
             * 这里改成允许的路径前缀，可以根据自己网站的用户登录态判断允许上传的具体路径
             * 资源表达式规则分对象存储(cos)和数据万象(ci)两种
             * 数据处理、审核相关接口需要授予ci资源权限
             * cos : qcs::cos:{region}:uid/{appid}:{bucket}/{path}
             * ci  : qcs::ci:{region}:uid/{appid}:bucket/{bucket}/{path}
             * 列举几种典型的{path}授权场景：
             * 1、允许访问所有对象："*"
             * 2、允许访问指定的对象："a/a1.txt", "b/b1.txt"
             * 3、允许访问指定前缀的对象："a*", "a/ *", "b/ *"
             * 如果填写了“*”，将允许用户访问所有资源；除非业务需要，否则请按照最小权限原则授予用户相应的访问权限范围。
             *
             * 示例：授权examplebucket-1250000000 bucket目录下的所有资源给cos和ci 授权两条Resource
             */
            statement.addResources(
                arrayOf(
                    "qcs::cos:ap-shanghai:uid/1317756102:file-xfpost-1317756102/*",
                    "qcs::ci:ap-shanghai:uid/1317756102:bucket/file-xfpost-1317756102/*"
                )
            )

            // 把一条 statement 添加到 policy
            // 可以添加多条
            policy.addStatement(statement)
            // 将 Policy 示例转化成 String，可以使用任何 json 转化方式，这里是本 SDK 自带的推荐方式
            config["policy"] = Jackson.toJsonPrettyString(policy)

            val response = CosStsClient.getCredential(config)
//            println(response.credentials.tmpSecretId)
//            println(response.credentials.tmpSecretKey)
//            println(response.credentials.sessionToken)
            val creJson = mutableMapOf<String, Any>()
            creJson["tmpSecretId"] = response.credentials.tmpSecretId
            creJson["tmpSecretKey"] = response.credentials.tmpSecretKey
            creJson["sessionToken"] = response.credentials.sessionToken
            val result = mutableMapOf<String, Any>()
            result["credentials"] = creJson
            result["startTime"] = response.startTime
            result["expiredTime"] = response.expiredTime
            return result
        } catch (e: Exception) {
            e.printStackTrace()
            throw IllegalArgumentException("no valid secret !")
        }
    }


    // 创建 COSClient 实例，这个实例用来后续调用请求
    fun createCOSClient(): COSClient {
        // 设置用户身份信息。
        // SECRETED 和 SECRETARY 请登录访问管理控制台 https://console.cloud.tencent.com/cam/capi 进行查看和管理
        val secretId = "AKIDKer296uVv9OYXJzhQ5auIftzvhHfqwra" //用户的 SecretId，建议使用子账号密钥，授权遵循最小权限指引，降低使用风险。子账号密钥获取可参见 https://cloud.tencent.com/document/product/598/37140
        val secretKey = "Zv88xaFk27zkLlzzpRt8C6n24wb3JVZh"
//        val secretId =
//            System.getenv("AKIDKer296uVv9OYXJzhQ5auIftzvhHfqwra") //用户的 SecretId，建议使用子账号密钥，授权遵循最小权限指引，降低使用风险。子账号密钥获取可参见 https://cloud.tencent.com/document/product/598/37140
//        val secretKey =
//            System.getenv("Zv88xaFk27zkLlzzpRt8C6n24wb3JVZh") //用户的 SecretKey，建议使用子账号密钥，授权遵循最小权限指引，降低使用风险。子账号密钥获取可参见 https://cloud.tencent.com/document/product/598/37140
        val cred: COSCredentials = BasicCOSCredentials(secretId, secretKey)

        // ClientConfig 中包含了后续请求 COS 的客户端设置：
        val clientConfig: ClientConfig = ClientConfig()

        // 设置 bucket 的地域
        // COS_REGION 请参见 https://cloud.tencent.com/document/product/436/6224
        clientConfig.region = Region("ap-shanghai")

        // 以下的设置，是可选的：

        // 设置 socket 读取超时，默认 30s
        clientConfig.socketTimeout = 30*1000;
        // 设置建立连接超时，默认 30s
        clientConfig.connectionTimeout = 30*1000;

        // 如果需要的话，设置 http 代理，ip 以及 port
        // clientConfig.setHttpProxyIp("httpProxyIp");
        // clientConfig.setHttpProxyPort(80);

        // 生成 cos 客户端。
        return COSClient(cred, clientConfig)
    }
}
