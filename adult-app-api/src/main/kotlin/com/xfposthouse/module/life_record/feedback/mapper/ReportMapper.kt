package com.xfposthouse.module.life_record.feedback.mapper

import com.xfposthouse.module.life_record.entity.LifeRecordEntity
import com.xfposthouse.module.life_record.entity.LifeReportEntity
import com.xfposthouse.user.entity.XFUserEntity
import com.xfposthouse.mapper.StringToArrayTypeHandler
import org.apache.ibatis.annotations.Insert
import org.apache.ibatis.annotations.Result
import org.apache.ibatis.annotations.ResultMap
import org.apache.ibatis.annotations.Results
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.apache.ibatis.annotations.Delete
import org.apache.ibatis.annotations.One
import org.springframework.stereotype.Repository
import com.mybatisflex.core.BaseMapper

@Repository

interface ReportMapper : BaseMapper<LifeReportEntity> {

    @Select("SELECT * FROM life_report WHERE author_id = #{authorId}")
    @Results(
        id = "reportMap", value = [
            Result(property = "id", column = "id"),
            Result(property = "title", column = "title"),
            Result(property = "description", column = "description"),
            Result(property = "createTime", column = "create_time"),
            Result(property = "imageUrls", column = "image_urls", typeHandler = StringToArrayTypeHandler::class),
            Result(property = "blog_id", column = "blogId"),
            Result(property = "authorInfo", column = "author_id", javaType = XFUserEntity::class, one = One(resultMap = "com.xfposthouse.mapper.UserMapper.userMap")),
        ]
    )
    fun selectReportByAuthorId(authorId: Int): List<LifeReportEntity>

    // 查询所有生活记录，并将 users 表的信息联接进来
    @Select("SELECT * FROM life_report")
    @ResultMap("reportMap")
    fun getReports(): List<LifeReportEntity>

    @Insert(
        """
        INSERT INTO life_report (title, description, create_time, image_urls, blog_id, author_id)
        VALUES (#{title}, #{description}, #{createTime}, #{imageUrls,typeHandler=com.xfposthouse.mapper.StringToArrayTypeHandler}, #{blogId}, #{authorInfo.userId})
    """
    )
    fun insertReport(lifeRecord: LifeReportEntity): Int

    @Select("SELECT * FROM life_report WHERE id = #{id}")
    fun selectLifeRecordById(id: Int): LifeReportEntity?

    @Delete("DELETE FROM life_report WHERE id = #{id}")
    fun deleteReport(id: Int): Int
}
