package com.xfposthouse.config

import com.xfposthouse.mapper.StringToArrayTypeHandler
import org.apache.ibatis.type.LocalDateTimeTypeHandler
import org.mybatis.spring.boot.autoconfigure.ConfigurationCustomizer
import org.mybatis.spring.mapper.MapperScannerConfigurer
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class MySqlConfig {


    @Bean
    fun configurationCustomizer(): ConfigurationCustomizer {
        return ConfigurationCustomizer { configuration ->
            configuration.typeHandlerRegistry.register(List::class.java, StringToArrayTypeHandler::class.java)
            configuration.typeHandlerRegistry.register(LocalDateTimeTypeHandler::class.java)
        }
    }
}