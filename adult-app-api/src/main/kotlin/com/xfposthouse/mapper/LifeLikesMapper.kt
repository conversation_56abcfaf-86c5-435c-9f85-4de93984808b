package com.xfposthouse.mapper

import com.xfposthouse.module.life_record.entity.LifeLikeEntity
import org.apache.ibatis.annotations.*
import org.apache.ibatis.session.RowBounds
import org.springframework.stereotype.Repository
import com.mybatisflex.core.BaseMapper


@Repository
interface LifeLikesMapper : BaseMapper<LifeLikeEntity> {

    // 定义结果映射
    @Select("SELECT * FROM life_comment WHERE user_id = #{userId} ORDER BY created_at DESC")
    @Results(
        id = "lifeLikesMap", // 为结果映射定义一个名称
        value = [
            Result(property = "id", column = "id"),
            Result(property = "postId", column = "post_id"),
            Result(property = "userId", column = "user_id"),
            Result(property = "createdAt", column = "created_at"),
        ]
    )
    fun getLikesByUserId(@Param("userId") userId: Long): List<LifeLikeEntity>

    // 插入点赞（安全去重）
    @Insert("INSERT INTO life_likes (post_id, user_id, created_at) " +
            "VALUES (#{postId}, #{userId}, #{createdAt})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    fun insertLike(likeEntity: LifeLikeEntity): Int

    // 删除点赞记录
    @Delete("DELETE FROM life_likes WHERE post_id = #{postId} AND user_id = #{userId}")
    fun deleteLike(@Param("postId") postId: Long?, @Param("userId") userId: Int?): Int

    // 存在性检查（高性能EXISTS）
    @Select("SELECT EXISTS(SELECT 1 FROM life_likes WHERE post_id = #{postId} AND user_id = #{userId})")
    fun existsByCompositeId(@Param("postId") postId: Long?, @Param("userId") userId: Int?): Boolean

    // 统计帖子点赞量（带缓存）
    @Select("SELECT COUNT(*) FROM life_likes WHERE post_id = #{postId}")
    @Options(useCache = true, flushCache = Options.FlushCachePolicy.FALSE)
    fun countByPostId(@Param("postId") postId: Long?): Int

    // 分页查询用户点赞记录
    @Select("SELECT * FROM life_likes WHERE user_id = #{userId} ORDER BY created_at DESC")
    @ResultMap("lifeLikesMap")
    fun selectByUserWithPaging(@Param("userId") userId: Long?, rowBounds: RowBounds?): List<LifeLikeEntity?>?

}
