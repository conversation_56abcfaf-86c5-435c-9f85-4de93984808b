package com.xfposthouse.module.life_record.feedback.mapper

import com.xfposthouse.module.life_record.entity.LifeRecordEntity
import com.xfposthouse.user.entity.XFUserEntity
import com.xfposthouse.mapper.StringToArrayTypeHandler
import org.apache.ibatis.annotations.Insert
import org.apache.ibatis.annotations.Result
import org.apache.ibatis.annotations.ResultMap
import org.apache.ibatis.annotations.Results
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.apache.ibatis.annotations.Delete
import org.apache.ibatis.annotations.One
import org.springframework.stereotype.Repository
import com.mybatisflex.core.BaseMapper

@Repository

interface FeedBackMapper : BaseMapper<LifeRecordEntity> {

    @Select("SELECT * FROM feed_back WHERE author_id = #{authorId}")
    @Results(
        id = "feedBackMap", value = [
            Result(property = "id", column = "id"),
            Result(property = "title", column = "title"),
            Result(property = "description", column = "description"),
            Result(property = "createTime", column = "create_time"),
            Result(property = "publishTime", column = "publish_time"),
            Result(property = "fileNames", column = "file_names", typeHandler = StringToArrayTypeHandler::class),
            Result(property = "imageUrls", column = "image_urls", typeHandler = StringToArrayTypeHandler::class),
            Result(property = "videoUrl", column = "video_url"),
            Result(property = "videoFileName", column = "video_file_name"),
            Result(property = "authorInfo", column = "author_id", javaType = XFUserEntity::class, one = One(resultMap = "com.xfposthouse.mapper.UserMapper.userMap")),
        ]
    )
    fun selectFeedBacksByAuthorId(authorId: Int): List<LifeRecordEntity>

    // 查询所有生活记录，并将 users 表的信息联接进来
    @Select("SELECT * FROM feed_back")
    @ResultMap("feedBackMap")
    fun getFeedBacks(): List<LifeRecordEntity>

    @Insert(
        """
        INSERT INTO feed_back (title, description, create_time, file_names, image_urls, video_url, video_file_name, author_id)
        VALUES (#{title}, #{description}, #{createTime}, #{fileNames,typeHandler=com.xfposthouse.mapper.StringToArrayTypeHandler}, #{imageUrls,typeHandler=com.xfposthouse.mapper.StringToArrayTypeHandler}, #{videoUrl}, #{videoFileName}, #{authorInfo.userId})
    """
    )
    fun insertFeedBack(lifeRecord: LifeRecordEntity): Int

    @Select("SELECT * FROM feed_back WHERE id = #{id}")
    fun selectLifeRecordById(id: Int): LifeRecordEntity?

    @Update(
        """
        UPDATE feed_back
        SET title = #{title}, description = #{description}, create_time = #{createTime},
            file_names = #{fileNames,typeHandler=com.xfposthouse.mapper.StringToArrayTypeHandler}, image_urls = #{imageUrls,typeHandler=com.xfposthouse.mapper.StringToArrayTypeHandler}, video_url = #{videoUrl},
            video_file_name = #{videoFileName}, author_id = #{author.userId}, updated_at = CURRENT_TIMESTAMP
        WHERE id = #{id}
    """
    )
    fun updateFeedBack(lifeRecord: LifeRecordEntity): Int

    @Delete("DELETE FROM feed_back WHERE id = #{id}")
    fun deleteFeedBack(id: Int): Int
}
