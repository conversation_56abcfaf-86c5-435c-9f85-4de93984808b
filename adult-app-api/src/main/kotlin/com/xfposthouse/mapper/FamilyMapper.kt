package com.xfposthouse.mapper

import com.mybatisflex.core.BaseMapper
import com.xfposthouse.module.family.FamilyEntity
import org.apache.ibatis.annotations.*
import org.springframework.stereotype.Repository

@Repository
interface FamilyMapper: BaseMapper<FamilyEntity> {

    // 查询家庭成员
    @Select("SELECT user_id FROM family_members where family_id = #{familyId}")
    fun findMembersById(familyId: Int): List<Int>

    @Select("SELECT * FROM family where id = #{familyId}")
    @Results(
        id = "familyMap", // 为结果映射定义一个名称
        value = [
            Result(property = "id", column = "id"),
            Result(property = "name", column = "name"),
            Result(property = "userId", column = "user_id"),
            Result(property = "inviteCode", column = "invite_code"),
        ]
    )
    fun findFamily(familyId: Int): FamilyEntity?

    @Insert("INSERT INTO family (name, user_id, invite_code) VALUES (#{name}, #{userId}, #{inviteCode})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    fun insertFamily(familyEntity: FamilyEntity): Int

    @Insert("INSERT INTO family_members (user_id, family_id) VALUES (#{userId}, #{familyId})")
    fun addMember(familyId: Int, userId: Int): Int
}