package com.xfposthouse.user.api
import com.xfposthouse.mapper.SmsCodeMapper
import com.xfposthouse.network.entity.XFErrorCode
import com.xfposthouse.network.entity.XFResponseEntity
import com.xfposthouse.user.entity.SmsCodeEntity
import com.tencentcloudapi.common.Credential
import com.tencentcloudapi.common.Sign
import com.tencentcloudapi.sms.v20190711.SmsClient
import com.tencentcloudapi.sms.v20190711.models.SendSmsRequest
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.*
import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec

@RestController
@RequestMapping("/api")
class SmsCodeServer {
    @Autowired
    lateinit var smsCodeMapper: SmsCodeMapper

    private var smsClient: SmsClient? = null

    private val secretKey = "g8MOGwPgkdJagZFhz4wa1GLDrJZdS4Vi"

    private val secretId = "AKIDgS3TggS1Pk054qRs57wYw87tRmaDftvk"

    private val appId = "1400928556"

    private var signSet: LinkedHashMap<String, Long> = LinkedHashMap(1000, 0.75F)

    @PostMapping("/user/getCode")
    fun sendSmsCode(@RequestBody param: Map<String, Any>): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            /// 校验参数
            val phoneNum: String = param["phone"].toString()
            val signStr = param["sign"].toString()
            val time = param["time"].toString().toLong()
            if (checkCodeParam(phoneNum, signStr, time)) {
                sendSms(phoneNum)
            } else {
                result.setError(XFErrorCode.PARAM_INVALID)
            }

        } catch (e: Error) {
            result.setError(XFErrorCode.SERVER_ERROR)
        }
        return  result
    }

    /// 校验签名
    private fun checkCodeParam(phoneNum: String, signStr: String, time: Long): Boolean {
        if (signSet.containsKey(signStr)) {
            return false
        }
        val sign = generateHmacSignature("zxzzlzq", "phone=$phoneNum&time=$time")
        signSet[signStr] = time
        return sign == signStr
    }

    /**
     * 生成 HMAC-SHA256 签名（与 Flutter 逻辑一致）
     */
    private fun generateHmacSignature(key: String, content: String): String {
        // 将密钥和内容转换为字节数组
        val keyBytes = key.toByteArray(Charsets.UTF_8)
        val contentBytes = content.toByteArray(Charsets.UTF_8)

        // 创建 HMAC-SHA256 签名器
        val mac = Mac.getInstance("HmacSHA256")
        val secretKeySpec = SecretKeySpec(keyBytes, "HmacSHA256")
        mac.init(secretKeySpec)

        // 计算 HMAC 值
        val hmacBytes = mac.doFinal(contentBytes)

        // 将字节数组转换为十六进制字符串
        return hmacBytes.toHex()
    }

    private fun ByteArray.toHex(): String {
        return joinToString(separator = "") { byte -> "%02x".format(byte) }
    }


    // 阿里云的短信发送逻辑
    fun sendSms(phoneNumber: String?) {
        // 生成验证码
        val verificationCode = generateVerificationCode()

        // 获取当前时间
        val sendTime = Date()

        // 保存验证码到数据库
        smsCodeMapper.saveSmsCode(phoneNumber, verificationCode, sendTime)


        // 发送短信
        sendSmsToPhone(phoneNumber, verificationCode)
    }

    // 生成6位数字验证码
    private fun generateVerificationCode(): String {
        val code = (Math.random() * 900000).toInt() + 100000 // 生成6位验证码
        return code.toString()
    }

    // 使用阿里云发送验证码短信
    private fun sendSmsToPhone(phoneNumber: String?, verificationCode: String) {
        if (smsClient == null) {
            smsClient = SmsClient(Credential(secretId, secretKey), "ap-guangzhou")
        }
        val req = SendSmsRequest()
        req.smsSdkAppid = appId        // 短信应用 ID
        req.sign = "成人记"                // 已审核的签名
        req.templateID = "2379969"             // 已审核的模板 ID
        req.templateParamSet = arrayOf(verificationCode)// 模板参数（需与模板变量匹配）
        req.phoneNumberSet = arrayOf(         // 国际格式手机号（E.164）
            "+86$phoneNumber",
        )
        smsClient?.SendSms(req)
    }

    /// 校验验证码
    fun checkCode(code: String, phoneNumber: String?): Boolean {
        val codeEntity: SmsCodeEntity? = smsCodeMapper.findSmsCodeByPhoneNumber(phoneNumber)
        if (codeEntity != null) {
            if (codeEntity.verificationCode != code) {
                return false
            }
            val codeTime = codeEntity.sendTime?.time?:0
            val nowTime = Date().time
            return nowTime - codeTime < 3 * 60 * 1000
        }
        return false
    }
}
