package com.xfposthouse.network

import com.xfposthouse.network.entity.XFErrorCode
import com.xfposthouse.network.entity.XFResponseEntity
import com.google.gson.Gson
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody

val httpClient = OkHttpClient()

class XFNetWork {

    companion object {
        val instance = XFNetWork()
    }

    private var headersBuilder = Headers.Builder()

    private  fun initHeaders() {
        headersBuilder["Content-Type"] = "application/json"
    }

    fun updateHeaders(headers: Map<String, Any>) {
        headersBuilder["Content-Type"] = "application/json"
        headers.forEach { (key, value) ->
            headersBuilder[key] = value.toString()
        }
    }

    /// 同步获取数据
    fun fetchRemoteData(url: String, params: Map<String, Any>?): XFResponseEntity {
        initHeaders()
        val result = XFResponseEntity()
        try {
            // 创建JSON MediaType
            val jsonMediaType = "application/json; charset=utf-8".toMediaType()
            // 创建要发送的JSON字符串
            var json = "{}"
            params?.let {
                json = Gson().toJson(params)
            }
            // 创建RequestBody
            val requestBody = json.toRequestBody(jsonMediaType)
            val request = Request.Builder()
                .url(url)
                .post(requestBody)
                .headers(headersBuilder.build())
                .build()
            val response = httpClient.newCall(request).execute()
            if (response.isSuccessful) {
                Gson().fromJson(response.body?.string(), XFResponseEntity::class.java)?.let { it
                    if (it.result == null) {
                        it.result = it.data
                        it.data = null
                    }
                    return it
                }
                result.code = 0
                result.msg = "OK"
                result.result = response.body?.string()
            } else {
                result.code = XFErrorCode.SERVER_ERROR.code
                result.msg = XFErrorCode.SERVER_ERROR.message
            }
        } catch (error: Exception) {
            result.code = XFErrorCode.SERVER_ERROR.code
            result.msg = XFErrorCode.SERVER_ERROR.message
        }
        return result
    }

    /// 发送string数据
    fun post(url: String, msg: String?): XFResponseEntity {
        initHeaders()
        val result = XFResponseEntity()
        try {
            // 创建JSON MediaType
            val jsonMediaType = "application/json; charset=utf-8".toMediaType()
            // 创建要发送的JSON字符串
            val json = msg?:""
            // 创建RequestBody
            val requestBody = json.toRequestBody(jsonMediaType)
            val request = Request.Builder()
                .url(url)
                .post(requestBody)
                .headers(headersBuilder.build())
                .build()
            val response = httpClient.newCall(request).execute()
            println(response)
            if (response.isSuccessful) {
                Gson().fromJson(response.body?.string(), XFResponseEntity::class.java)?.let { it
                    if (it.result == null) {
                        it.result = it.data
                        it.data = null
                    }
                    return it
                }
                result.code = 0
                result.msg = "OK"
                result.result = response.body?.string()
            } else {
                result.code = XFErrorCode.SERVER_ERROR.code
                result.msg = XFErrorCode.SERVER_ERROR.message
            }
        } catch (error: Exception) {
            println(error.message)
            result.code = XFErrorCode.SERVER_ERROR.code
            result.msg = XFErrorCode.SERVER_ERROR.message
        }
        return result
    }

    fun fetchChatData(url: String, params: Map<String, Any>?): XFResponseEntity {
        initHeaders()
        val result = XFResponseEntity()
        try {
            // 创建JSON MediaType
            val jsonMediaType = "application/json; charset=utf-8".toMediaType()
            // 创建要发送的JSON字符串
            var json = "{}"
            params?.let {
                json = Gson().toJson(params)
            }
            // 创建RequestBody
            val requestBody = json.toRequestBody(jsonMediaType)
            val request = Request.Builder()
                    .url(url)
                    .post(requestBody)
                    .headers(headersBuilder.build())
                    .build()
            val response = httpClient.newCall(request).execute()
            if (response.isSuccessful) {
//                Gson().fromJson(response.body?.string(), XFResponseEntity::class.java)?.let { it
//                    if (it.result == null) {
//                        it.result = it.data
//                        it.data = null
//                    }
//                    return it
//                }
//                println(response.body)
                println(response.message)
                result.code = 0
                result.msg = "OK"
                result.result = response.body?.string()?.let { handleChatData(it) }
            } else {
                println("SERVER_ERROR")
                result.code = XFErrorCode.SERVER_ERROR.code
                result.msg = XFErrorCode.SERVER_ERROR.message
            }
        } catch (error: Exception) {
            println(error)
            result.code = XFErrorCode.SERVER_ERROR.code
            result.msg = XFErrorCode.SERVER_ERROR.message
        }
        return result
    }

    private fun handleChatData(string: String): String {
        val resultStr = StringBuilder()

        // 假设 entity.result 是一个JSON格式的字符串
        val jsonList = string.split("event:message").drop(1) // 去掉split后的第一个空字符串

        jsonList.forEach { str ->
            val startOfDelta = "\"delta\":"
            val startIndex = str.indexOf(startOfDelta)
            if (startIndex != -1) {
                val jsonPart = str.substring(startIndex + startOfDelta.length)
                val endIndex = jsonPart.indexOf(',')
                if (endIndex != -1) {
                    val trimmedJsonStr = jsonPart.substring(0, endIndex).trim()
                    if (trimmedJsonStr.contains("content")) {
                        // 解析JSON字符串
                        val jsonObject = Gson().fromJson(trimmedJsonStr, Map::class.java)
                        // 获取"content"字段
                        val content = jsonObject["content"]
                        resultStr.append(content)
                    }
                }
            }
        }
        return resultStr.toString()
    }
}