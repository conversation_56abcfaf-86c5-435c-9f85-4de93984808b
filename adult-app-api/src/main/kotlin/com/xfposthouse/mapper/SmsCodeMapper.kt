package com.xfposthouse.mapper

import com.xfposthouse.user.entity.SmsCodeEntity
import org.apache.ibatis.annotations.*
import org.apache.ibatis.annotations.Result
import com.mybatisflex.core.BaseMapper
import org.springframework.stereotype.Repository
import java.util.*

@Repository
interface SmsCodeMapper: BaseMapper<SmsCodeEntity> {
    // 保存验证码记录
    @Insert(
        """
    INSERT INTO xf_sms_code (phone_number, verification_code, send_time)
    VALUES (#{phoneNumber}, #{verificationCode}, #{sendTime})
    ON DUPLICATE KEY UPDATE
        verification_code = VALUES(verification_code),
        send_time = VALUES(send_time)
    """
    )
    fun saveSmsCode(
        @Param("phoneNumber") phoneNumber: String?,
        @Param("verificationCode") verificationCode: String?,
        @Param("sendTime") sendTime: Date?
    )

    // 根据手机号查询验证码
    @Select("SELECT * FROM xf_sms_code WHERE phone_number = #{phoneNumber}")
    @Results(value = [
        Result(property = "id", column = "id"),
        Result(property = "phoneNumber", column = "phone_number"),
        Result(property = "verificationCode", column = "verification_code"),
        Result(property = "sendTime", column = "send_time"),
        Result(property = "status", column = "status"),
        ]
    )
    fun findSmsCodeByPhoneNumber(phoneNumber: String?): SmsCodeEntity?

    // 更新验证码状态（标记为已使用）
    @Update("UPDATE xf_sms_code SET status = 1 WHERE phone_number = #{phoneNumber}")
    fun updateSmsCodeStatus(@Param("phoneNumber") phoneNumber: String?)
}