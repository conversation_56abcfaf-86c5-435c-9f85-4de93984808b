plugins {
    id("org.springframework.boot") version "3.4.4"
    id("io.spring.dependency-management") version "1.1.7"
    kotlin("jvm") version "2.1.20"
    kotlin("plugin.spring") version "2.1.20"
}

dependencies {
    // ✅ 项目内部模块依赖
    implementation(project(":adult-common"))
    implementation(project(":adult-server"))
    implementation(project(":adult-dao"))

    // 🧰 Spring Boot & Web 框架
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.mybatis.spring.boot:mybatis-spring-boot-starter:3.0.4")

    // 🗃️ 数据库相关
    implementation("com.mysql:mysql-connector-j:9.3.0")
    implementation("com.mybatis-flex:mybatis-flex-spring-boot-starter:1.10.9")
    // implementation("com.mybatis-flex:mybatis-flex-kotlin:1.0.2") // 可选 Kotlin 扩展

    // 🔐 安全与认证
    implementation("com.auth0:java-jwt:3.18.1")
    // Sa-Token 权限认证
    implementation("cn.dev33:sa-token-spring-boot-starter:1.44.0")

    // 🔗 HTTP 客户端与远程调用
    implementation("com.squareup.okhttp3:okhttp:5.0.0-alpha.3")
    implementation("org.apache.httpcomponents:httpclient:4.5.13")

    // 💳 支付 SDK
    implementation("com.alipay.sdk:alipay-sdk-java:4.39.79.ALL")
    implementation("com.github.wechatpay-apiv3:wechatpay-java:0.2.12")

    // ☁️ 云服务 SDK
    implementation("com.qcloud:cos-sts_api:3.1.0")
    implementation("com.qcloud:cos_api:5.6.227")
    implementation("com.tencentcloudapi:tencentcloud-sdk-java-sms:3.1.1179")

    // 📦 序列化 / JSON 解析
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin")
    implementation("com.google.code.gson:gson:2.10.1")

    // 🧠 Kotlin 基础支持
    implementation("org.jetbrains.kotlin:kotlin-reflect")
    implementation("io.github.oshai:kotlin-logging-jvm:7.0.4")
    // implementation(kotlin("stdlib-jdk8")) // 通常自动包含

    // 🧱 工具类 / 通用依赖
    // implementation("cn.hutool:hutool-all:5.8.38")
    implementation("cn.hutool.v7:hutool-parent:7.0.0-M1")
    implementation("org.apache.commons:commons-lang3:3.18.0")

    // 📝 日志与格式化
    implementation("net.logstash.logback:logstash-logback-encoder:7.4")

//    implementation(project(":adult-common"))
//    implementation("org.springframework.boot:spring-boot-starter-web")
//    implementation("com.fasterxml.jackson.module:jackson-module-kotlin")
//    implementation("org.jetbrains.kotlin:kotlin-reflect")
//    implementation("org.redisson:redisson:3.50.0")
//    implementation("org.mybatis.spring.boot:mybatis-spring-boot-starter:3.0.4")
//    implementation("com.mysql:mysql-connector-j:9.3.0")
//    implementation("tk.mybatis:mapper:4.2.2")
//    implementation("com.auth0:java-jwt:3.18.1")
//    implementation("com.google.code.gson:gson:2.10.1")
//    implementation("com.alipay.sdk:alipay-sdk-java:4.39.79.ALL")
//    implementation("com.squareup.okhttp3:okhttp:5.0.0-alpha.3")
//    implementation("com.github.wechatpay-apiv3:wechatpay-java:0.2.12")
//    implementation("cn.hutool:hutool-all:5.8.38")
//    implementation("org.apache.commons:commons-lang3:3.12.0")
//    implementation("org.apache.httpcomponents:httpclient:4.5.13")
//    implementation("com.qcloud:cos-sts_api:3.1.0")
//    implementation("com.tencentcloudapi:tencentcloud-sdk-java-sms:3.1.1179")
//    implementation("com.qcloud:cos_api:5.6.227")
//    implementation("io.github.oshai:kotlin-logging-jvm:7.0.4")
//    implementation("net.logstash.logback:logstash-logback-encoder:7.4")
//    // implementation("com.mybatis-flex:mybatis-flex-kotlin:1.0.2")
//    implementation("com.mybatis-flex:mybatis-flex-spring-boot-starter:1.10.9")
    testImplementation("org.springframework.boot:spring-boot-starter-test")
}