package com.xfposthouse.mapper

import com.mybatisflex.core.BaseMapper
import com.xfposthouse.module.ledger.LedgerBookEntity
import com.xfposthouse.module.ledger.LedgerEntity
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import java.math.BigDecimal
import java.time.LocalDateTime


interface LedgerMapper: BaseMapper<LedgerEntity> {

    @Select("""
    SELECT 
        DATE_FORMAT(record_date, '%Y-%m') AS month,
        SUM(CASE WHEN type = 1 THEN amount ELSE 0 END) AS income,
        SUM(CASE WHEN type = 0 THEN amount ELSE 0 END) AS expense
    FROM ledger
    WHERE user_id = #{userId}
      AND book_id = #{bookId}
      AND record_date >= DATE_FORMAT(#{startDate}, '%Y-%m-01')  -- 起始月份的第一天
      AND record_date < DATE_FORMAT(DATE_ADD(#{endDate}, INTERVAL 1 MONTH), '%Y-%m-01')  -- 结束月份的下一个月的第一天
    GROUP BY month
    ORDER BY month DESC
""")
    fun getMonthlyExpenseStats(
        @Param("userId") userId: Long,
        @Param("bookId") bookId: Long,
        @Param("startDate") startDate: LocalDateTime,
        @Param("endDate") endDate: LocalDateTime
    ): List<Map<String, Any>>

    @Select("""
            SELECT SUM(CASE WHEN type=1 THEN amount ELSE 0 END) AS incomeAmount,
            SUM(CASE WHEN type=0 THEN amount ELSE 0 END) AS outAmount
            FROM ledger WHERE book_id = #{bookId}
        """)
    fun getAmountSummary(@Param("bookId") bookId: Long): MutableMap<String?, BigDecimal?>?

    @Select("""
        SELECT book_id FROM ledger WHERE id = #{id}
    """)
    fun getBookIdWithLedgerId(id: Long): Long?
}

interface LedgerBookMapper : BaseMapper<LedgerBookEntity> {

}